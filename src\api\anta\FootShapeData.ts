import request from '/@/utils/request';

export function fetchList(query?: Object) {
	return request({
		url: '/api/footShapeData/getFootData',
		method: 'get',
		params: query,
	});
}

export function addObj(obj?: Object) {
	return request({
		url: '/anta/footShapeData',
		method: 'post',
		data: obj,
	});
}

export function getObj(id?: string) {
	return request({
		url: '/anta/footShapeData/' + id,
		method: 'get',
	});
}

export function delObjs(ids?: Object) {
	return request({
		url: '/anta/footShapeData',
		method: 'delete',
		data: ids,
	});
}

export function putObj(obj?: Object) {
	return request({
		url: '/anta/footShapeData',
		method: 'put',
		data: obj,
	});
}

export function delChildObj(ids?: Object) {
	return request({
		url: '/anta/footShapeData/child',
		method: 'delete',
		data: ids,
	});
}

/**
 * 分页查询足型数据（根据手机号/时间）
 * @param query 查询参数
 */
export function fetchFootShapePageByPhone(query?: Object) {
	return request({
		url: '/api/footShapeData/pageByPhone',
		method: 'get',
		params: query,
	});
}

/**
 * 导出足型数据（根据手机号/时间）
 * @param query 查询参数
 */
export function exportFootShapeByPhone(query?: Object) {
	return request({
		url: '/api/footShapeData/exportByPhone',
		method: 'get',
		params: query,
		responseType: 'blob', // 导出文件流
	});
}
