<template>
  <el-dialog :title="form.id ? '编辑' : '新增'" v-model="visible" :close-on-click-modal="false" draggable
    :show-close="false" :width="50 + '%'">
    <el-form ref="dataFormRef" :model="form" :rules="dataRules" formDialogRef label-width="90px" v-loading="loading">
      <el-row :gutter="24">
        <el-col :span="12" class="mb20">
          <el-form-item label="标题" prop="title">
            <el-input v-model="form.title" placeholder="请输入标题" />
          </el-form-item>
        </el-col>


        <el-col :span="12" class="mb20">
          <el-form-item label="类型" prop="type">
            <el-select v-model="form.type" placeholder="请选择类型">
              <el-option label="鞋子" value="shoes"></el-option>
              <el-option label="鞋垫" value="insole"></el-option>
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :span="12" class="mb20">
          <el-form-item label="特征" prop="feature">
            <el-select v-model="form.feature" placeholder="请选择特征">
              <el-option label="大童常规楦" value="大童常规楦"></el-option>
              <el-option label="大童宽楦" value="大童宽楦"></el-option>
              <el-option label="大童窄楦" value="大童窄楦"></el-option>
              <el-option label="小童宽楦" value="小童宽楦"></el-option>
              <el-option label="小童常规楦" value="小童常规楦"></el-option>
            </el-select>
          </el-form-item>
        </el-col>


        <el-col :span="12" class="mb20">
          <el-form-item label="性别" prop="gender">
            <el-select v-model="form.gender" placeholder="请选择性别">
              <el-option label="男" value="男"></el-option>
              <el-option label="女" value="女"></el-option>
            </el-select>
          </el-form-item>
        </el-col>


        <el-col :span="24" class="mb20">
          <el-form-item label="产品列表" prop="product">
            <div class="product-list-header">
              <el-button type="primary" @click="openProductSelector">选择产品</el-button>
            </div>
          </el-form-item>

          <!-- 产品列表展示 -->
          <el-table :data="productParam" border style="width: 100%; margin-bottom: 15px;">
            <el-table-column prop="code" label="商品编码" width="100" />
            <el-table-column prop="name" label="商品名称" />
            <el-table-column prop="amount" label="价格" width="80" />
            <el-table-column prop="gender" label="性别" width="60">
              <template #default="scope">
                <span>{{ scope.row.gender === 0 || scope.row.gender === '0' ? '男' : scope.row.gender === 1 ||
                  scope.row.gender === '1' ? '女' : '' }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="character" label="特征" width="100">
              <template #default="scope">
                <span>{{ formatCharacterDisplay(scope.row.character) }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="deptName" label="区域" width="120">
              <template #default="scope">
                <span>{{ getDeptNameById(scope.row) }}</span>
              </template>
            </el-table-column>
            <el-table-column label="商品图片" width="120">
              <template #default="scope">
                <el-image :src="scope.row.image" fit="contain" style="width: 60px; height: 60px; cursor: pointer;"
                  @click="handlePreviewImage(scope.row.image)">
                  <template #error>
                    <div class="image-slot">
                      <el-icon>
                        <Picture />
                      </el-icon>
                    </div>
                  </template>
                </el-image>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="150">
              <template #default="scope">
                <el-button v-if="isHeadquartersUser" type="primary" size="small" @click="handleEditProduct(scope.row)">
                  编辑
                </el-button>
                <el-button v-if="canRemoveProduct(scope.row)" type="danger" size="small"
                  @click="deleteItempro(scope.$index)">
                  {{ isHeadquartersUser ? '删除' : '移除' }}
                </el-button>
              </template>
            </el-table-column>
          </el-table>
          <!-- 
          <el-button type="primary" @click="handleAddProduct">新增产品</el-button> -->
        </el-col>
      </el-row>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="cancel">取消</el-button>
        <el-button type="primary" @click="onSubmit" :disabled="loading">确认</el-button>
      </span>
    </template>
  </el-dialog>

  <!-- 添加图片预览对话框 -->
  <el-dialog v-model="previewVisible" width="800px" :modal="true" :append-to-body="true" :close-on-click-modal="true"
    :show-close="true" class="preview-dialog" :z-index="3000">
    <div class="preview-container">
      <img :src="previewImage" style="max-width: 100%; max-height: 80vh; object-fit: contain;">
    </div>
  </el-dialog>

  <!-- 产品选择对话框 -->
  <el-dialog v-model="productSelectorVisible" title="选择产品" width="65%" append-to-body>
    <!-- 搜索表单 - 使用新的布局方式 -->
    <div class="search-form-container">
      <el-form :model="productSearch" class="search-form" inline>
        <div class="search-form-row">
          <el-form-item label="商品编码">
            <el-input v-model="productSearch.code" placeholder="请输入商品编码" clearable style="width: 120px" />
          </el-form-item>
          <el-form-item label="商品名称">
            <el-input v-model="productSearch.name" placeholder="请输入商品名称" clearable style="width: 120px" />
          </el-form-item>
          <el-form-item label="性别">
            <el-select v-model="productSearch.gender" placeholder="请选择性别" clearable style="width: 120px">
              <el-option label="男" :value="0"></el-option>
              <el-option label="女" :value="1"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="特征">
            <el-select v-model="productSearch.character" placeholder="请选择特征" clearable multiple collapse-tags
              style="width: 150px">
              <el-option label="大童常规楦" value="dtRegular"></el-option>
              <el-option label="大童宽楦" value="dtWidth"></el-option>
              <el-option label="大童窄楦" value="dtNarrow"></el-option>
              <el-option label="小童宽楦" value="xtWidth"></el-option>
              <el-option label="小童常规楦" value="xtRegular"></el-option>
            </el-select>
          </el-form-item>

          <el-form-item class="search-form-buttons">
            <el-button type="primary" icon="Search" @click="searchProducts">查询</el-button>
            <el-button icon="Refresh" @click="resetProductSearch">重置</el-button>
          </el-form-item>
        </div>
      </el-form>
      <div class="search-form-tip">特征窄楦的，推荐常规楦；特征常规楦的，推荐常规楦+宽楦的；特征宽楦的，推荐宽楦的</div>
    </div>

    <!-- 产品列表 -->
    <el-table :data="productList" v-loading="productListLoading" border height="600"
      @selection-change="handleProductSelectionChange" ref="productTableRef" row-key="code"
      :key="'product-table-' + forceRenderKey" class="product-selector-table">
      <el-table-column type="selection" width="40" align="center" />
      <el-table-column prop="code" label="商品编码" min-width="120" show-overflow-tooltip />
      <el-table-column prop="name" label="商品名称" min-width="150" show-overflow-tooltip />
      <el-table-column prop="amount" label="价格" width="80" show-overflow-tooltip />
      <el-table-column prop="gender" label="性别" width="60">
        <template #default="scope">
          <span>{{ scope.row.gender === 0 ? '男' : scope.row.gender === 1 ? '女' : '' }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="character" label="特征" width="100">
        <template #default="scope">
          <span>{{ formatCharacterDisplay(scope.row.character) }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="deptName" label="区域" width="120">
        <template #default="scope">
          <span>{{ getDeptNameById(scope.row) }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="image" label="商品图片" width="120">
        <template #default="scope">
          <el-image :src="scope.row.image" fit="contain" style="width: 60px; height: 60px; cursor: pointer;"
            @click="handlePreviewImage(scope.row.image)">
            <template #error>
              <div class="image-slot">
                <el-icon>
                  <Picture />
                </el-icon>
              </div>
            </template>
          </el-image>
        </template>
      </el-table-column>
    </el-table>

    <div class="pagination-container">
      <el-pagination v-model:current-page="productPagination.currentPage" v-model:page-size="productPagination.pageSize"
        :page-sizes="[10, 20, 50, 100]" background layout="total, sizes, prev, pager, next, jumper"
        :total="productPagination.total" @size-change="handleProductSizeChange"
        @current-change="handleProductCurrentChange" />
    </div>

    <!-- 底部按钮 -->
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="productSelectorVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmProductSelection">确认</el-button>
      </span>
    </template>
  </el-dialog>

  <!-- 产品编辑对话框 -->
  <el-dialog v-model="productEditVisible" :title="isNewProduct ? '新增产品' : '编辑产品'" width="500px">
    <el-form :model="productForm" ref="productFormRef" label-width="100px" :rules="productFormRules">
      <el-form-item label="商品编码" prop="code" required v-if="isNewProduct">
        <el-input v-model="productForm.code" placeholder="请输入商品编码" />
      </el-form-item>
      <el-form-item label="商品名称" prop="name" required>
        <el-input v-model="productForm.name" placeholder="请输入商品名称" />
      </el-form-item>
      <el-form-item label="价格" prop="amount" required>
        <el-input v-model="productForm.amount" placeholder="请输入价格" />
      </el-form-item>
      <el-form-item label="性别" prop="gender" required>
        <el-select v-model="productForm.gender" placeholder="请选择性别">
          <el-option label="男" value="0"></el-option>
          <el-option label="女" value="1"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="特征" prop="character" required>
        <el-select v-model="productForm.character" placeholder="请选择特征">
          <!-- 鞋子特征 -->
          <el-option label="大童常规楦" value="dtRegular"></el-option>
          <el-option label="大童宽楦" value="dtWidth"></el-option>
          <el-option label="大童窄楦" value="dtNarrow"></el-option>
          <el-option label="小童宽楦" value="xtWidth"></el-option>
          <el-option label="小童常规楦" value="xtRegular"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="内容列表" prop="content">
        <div v-for="(item, index) in productForm.content || []" :key="index" class="content-item">
          <el-input v-model="item.content" placeholder="请输入内容描述"
            style="width: calc(100% - 80px); margin-right: 10px;" />
          <el-button type="danger" @click="removeProductContent(index)"
            :disabled="(productForm.content || []).length <= 1">删除</el-button>
        </div>
        <div class="content-actions">
          <el-button type="primary" @click="addProductContent"
            :disabled="(productForm.content || []).length >= 5">添加内容</el-button>
          <span class="content-tip" v-if="(productForm.content || []).length >= 5">最多添加5个内容项</span>
        </div>
      </el-form-item>
      <el-form-item label="商品图片" prop="image" required>
        <upload-img v-model:image-url="productForm.image" :drag="true" :file-size="200" />
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="productEditVisible = false">取消</el-button>
        <el-button type="primary" @click="saveProductEdit">确认</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts" name="RecommendDialog">
import { useMessage, useMessageBox } from "/@/hooks/message";
import { getObj, addObj, putObj } from '/@/api/anta/recommend';
import { Picture } from '@element-plus/icons-vue';
import { fetchShoeTypeSearch } from "/@/api/anta/ShoeType";
import { fetchAddProduct, fetchUpdateProduct, fetchAddDeptAccess } from "/@/api/anta/recommend";
import { getUserInfo } from '/@/api/login/index';
import { deptTree } from '/@/api/admin/dept';

const emit = defineEmits(['refresh']);

// 定义变量内容
const dataFormRef = ref();
const visible = ref(false);
const loading = ref(false);

// 提交表单数据
const form = reactive({
  id: '',
  title: '',
  subTitle: '',
  gender: '',
  product: '',
  introduce: '',
  type: '',
  feature: '',
});

// 产品项目接口定义
interface ProductItem {
  code: string;
  name: string;
  amount: string;
  image: string;
  imgFile?: string;
  deviceId: string[];
  sort: number;
  gender: string | number;
  character: string;
  content?: { content: string }[];
  deptId?: string;
  deptIds?: string[]; // 支持多个部门ID
}

interface IntroduceItem {
  content: string;
}

// 产品相关数据
const productParam = reactive<ProductItem[]>([]);
const introduceParam = reactive<IntroduceItem[]>([]);

// 图片预览相关
const previewVisible = ref(false);
const previewImage = ref('');

// 表格渲染相关，强制重渲染时使用
const forceRenderKey = ref(0);

// 产品选择器相关
const productSelectorVisible = ref(false);
const productList = ref<any[]>([]);
const selectedProducts = ref<any[]>([]);
const selectedProductIds = ref<string[]>([]);
const selectedProductMap = ref<Record<string, any>>({});
const productListLoading = ref(false);
const productTableRef = ref();

// 记录每个页面的选中状态
const pageSelectionMap = ref<Record<number, string[]>>({});

// 产品搜索相关
const productSearch = reactive({
  name: '',
  code: '',
  gender: '' as any,
  character: [] as any[]
});

// 产品分页相关
const productPagination = reactive({
  currentPage: 1,
  pageSize: 10,
  total: 0
});

// 产品编辑相关
const productEditVisible = ref(false);
const productForm = ref<ProductItem>({
  code: '',
  name: '',
  amount: '',
  image: '',
  deviceId: [],
  sort: 0,
  gender: '',
  character: '',
  content: [{ content: '' }]
});
const productFormRef = ref();
const editingProductIndex = ref(-1);
const isNewProduct = ref(true);

// 产品表单验证规则
const productFormRules = {
  code: [{ required: true, message: '请输入商品编码', trigger: 'blur' }],
  name: [{ required: true, message: '请输入商品名称', trigger: 'blur' }],
  amount: [{ required: true, message: '请输入价格', trigger: 'blur' }],
  gender: [{ required: true, message: '请选择性别', trigger: 'change' }],
  character: [{ required: true, message: '请选择特征', trigger: 'change' }],
  image: [{ required: true, message: '请上传商品图片', trigger: 'change' }]
};

// 修复表格问题 - 使用Set数据结构来确保选中ID的唯一性和高效操作
const selectedProductSet = ref(new Set<string>());

// 用户信息和部门相关数据
const userInfo = ref<any>({});
const deptTreeData = ref<any[]>([]);
const userDeptId = ref<string>('');
const allowedDeptIds = ref<string[]>([]);

// 计算属性：判断是否为总部用户
const isHeadquartersUser = computed(() => {
  if (!userDeptId.value || !deptTreeData.value.length) return false;
  // 动态查找总部部门（通常是根节点或名称包含"总部"的部门）
  const headquartersId = findHeadquartersId();
  return headquartersId && userDeptId.value === headquartersId;
});

// 判断区域用户是否可以移除某个产品
const canRemoveProduct = (product: ProductItem): boolean => {
  // 总部用户可以删除任何产品
  if (isHeadquartersUser.value) {
    return true;
  }

  // 区域用户只能移除包含自己部门ID的产品
  if (product.deptIds && Array.isArray(product.deptIds)) {
    return product.deptIds.includes(userDeptId.value);
  }

  // 兼容旧的deptId字段
  if (product.deptId) {
    return product.deptId === userDeptId.value;
  }

  // 如果产品没有部门信息，区域用户不能移除（视为总部产品）
  return false;
};

// 图片预览处理函数
const handlePreviewImage = (imageUrl: string) => {
  previewImage.value = imageUrl;
  previewVisible.value = true;
};

// 打开产品选择器
const openProductSelector = () => {
  // 清空所有选择状态
  selectedProducts.value = [];
  selectedProductIds.value = [];
  selectedProductMap.value = {};
  selectedProductSet.value.clear();
  pageSelectionMap.value = {}; // 清空页面选择记录

  // 设置弹窗和分页
  productSelectorVisible.value = true;
  productPagination.currentPage = 1;

  // 重置搜索条件
  productSearch.name = '';
  productSearch.code = '';

  // 如果已经选择了性别和特征，自动带入搜索条件
  if (form.gender === '男') {
    productSearch.gender = 0;
  } else if (form.gender === '女') {
    productSearch.gender = 1;
  } else {
    productSearch.gender = '';
  }

  // 设置特征
  if (form.feature) {
    productSearch.character = [convertFeatureToCharacter(form.feature)];
  } else {
    productSearch.character = [];
  }

  // 执行搜索，确保表格内容被重置
  searchProducts();
};

// 添加特征字段翻译显示函数
const formatCharacterDisplay = (character: string) => {
  if (!character) return '';

  switch (character) {
    case 'dtRegular': return '大童常规楦';
    case 'dtWidth': return '大童宽楦';
    case 'dtNarrow': return '大童窄楦';
    case 'xtWidth': return '小童宽楦';
    case 'xtRegular': return '小童常规楦';
    case 'normal': return '正常';
    case 'lowArch': return '低足弓';
    case 'highArch': return '高足弓';
    default: return character;
  }
};

// 获取用户信息和部门权限
const initUserPermissions = async () => {
  try {
    // 获取用户信息
    const userRes = await getUserInfo();
    userInfo.value = userRes.data.sysUser;
    userDeptId.value = userInfo.value.deptId;

    // 获取部门树
    const deptRes = await deptTree();
    deptTreeData.value = deptRes.data;

    // 计算允许访问的部门ID列表
    calculateAllowedDeptIds();
  } catch (error) {
    useMessage().error('获取用户权限信息失败');
  }
};

// 计算允许访问的部门ID列表
const calculateAllowedDeptIds = () => {
  const allowed: string[] = [];

  // 动态查找总部ID
  const headquartersId = findHeadquartersId();

  // 如果是总部用户，不设置部门过滤（能看到所有产品）
  if (headquartersId && userDeptId.value === headquartersId) {
    allowedDeptIds.value = []; // 空数组表示不过滤
    return;
  }

  // 区域用户：如果能找到总部ID则包含总部，加上当前用户部门
  if (headquartersId) {
    allowed.push(headquartersId); // 总部ID
  }
  if (userDeptId.value && userDeptId.value !== headquartersId) {
    allowed.push(userDeptId.value); // 当前用户部门ID
  }

  allowedDeptIds.value = allowed;
};

// 根据部门ID获取部门名称
const getDeptNameById = (product: any): string => {
  if (!deptTreeData.value.length) return '总部';

  const findDeptName = (depts: any[], targetId: string): string => {
    for (const dept of depts) {
      if (dept.id === targetId) {
        return dept.name;
      }
      if (dept.children && dept.children.length > 0) {
        const found = findDeptName(dept.children, targetId);
        if (found) return found;
      }
    }
    return '';
  };

  // 优先使用deptIds数组
  if (product.deptIds) {
    let productDeptIds: string[] = [];

    if (Array.isArray(product.deptIds)) {
      productDeptIds = product.deptIds;
    } else if (typeof product.deptIds === 'string') {
      try {
        productDeptIds = JSON.parse(product.deptIds);
      } catch (e) {
        // 如果解析失败，尝试作为单个ID处理
        productDeptIds = [product.deptIds];
      }
    }

    if (productDeptIds.length > 0) {
      let displayDeptIds = productDeptIds;

      // 区域用户只显示相关的部门（总部 + 自己的部门）
      if (!isHeadquartersUser.value) {
        const headquartersId = findHeadquartersId();
        displayDeptIds = productDeptIds.filter((id: string) =>
          (headquartersId && id === headquartersId) || id === userDeptId.value
        );
      }

      const deptNames = displayDeptIds.map((id: string) => {
        const name = findDeptName(deptTreeData.value, id);
        return name || '总部';
      });
      return deptNames.join(', ');
    }
  }

  // 兼容旧的deptId字段
  if (product.deptId) {
    const deptName = findDeptName(deptTreeData.value, product.deptId);
    return deptName || '总部';
  }

  return '总部';
};

// 查找总部部门ID - 动态查找而不是硬编码
const findHeadquartersId = (): string => {
  if (!deptTreeData.value || deptTreeData.value.length === 0) {
    return '';
  }

  // 方法1: 查找名称包含"总部"的部门
  const findDeptByName = (depts: any[], targetName: string): string => {
    for (const dept of depts) {
      if (dept.name && dept.name.includes(targetName)) {
        return dept.id;
      }
      if (dept.children && dept.children.length > 0) {
        const found = findDeptByName(dept.children, targetName);
        if (found) return found;
      }
    }
    return '';
  };

  // 首先尝试查找名称包含"总部"的部门
  const headquartersByName = findDeptByName(deptTreeData.value, '总部');
  if (headquartersByName) {
    return headquartersByName;
  }

  // 方法2: 如果没有找到，使用根节点作为总部（通常第一个节点是总部）
  if (deptTreeData.value.length > 0 && deptTreeData.value[0].id) {
    return deptTreeData.value[0].id;
  }

  return '';
};



// 区域部门选择产品 - 建立访问权限关联，不改变产品归属
const addDeptAccess = async (productCode: string, deptIds: string[]) => {
  const params = {
    code: productCode,
    type: form.type || 'shoes',
    deptIds: deptIds
  };

  return await fetchAddDeptAccess(params);
};

// 更新产品信息 - 总部用户修改产品基本信息或归属
const updateProductInfo = async (productCode: string, updateData: any) => {
  const params = {
    code: productCode,
    type: form.type || 'shoes',
    operationType: 'UPDATE_INFO',  // 明确指定操作类型
    ...updateData
  };

  return await fetchUpdateProduct(params);
};



// 搜索产品
const searchProducts = async () => {
  try {
    productListLoading.value = true;
    const params: any = {
      name: productSearch.name,
      code: productSearch.code,
      current: productPagination.currentPage,
      size: productPagination.pageSize,
      type: form.type || 'shoes'
    };

    // 注意：不传递deptIds参数给后端，确保获取完整的产品部门信息
    // 避免后端过滤导致其他区域的deptIds丢失

    // 添加性别参数，转换为数字
    if (productSearch.gender !== '' && productSearch.gender !== null && productSearch.gender !== undefined) {
      params.gender = Number(productSearch.gender);
    }

    // 添加特征参数
    if (productSearch.character) {
      // 确保character始终是数组格式传递给后端
      const characterArray = Array.isArray(productSearch.character)
        ? productSearch.character.filter((c: any) => c && String(c).trim() !== '')
        : [productSearch.character].filter((c: any) => c && String(c).trim() !== '');

      if (characterArray.length > 0) {
        params.character = characterArray;
      }
    }

    const res = await fetchShoeTypeSearch(params);
    if (res.code === 0) {
      let allProducts = res.data.records || [];

      // 前端进行权限过滤：只显示当前用户有权限查看的产品
      // 但保留完整的deptIds信息用于合并
      if (!isHeadquartersUser.value && allowedDeptIds.value.length > 0) {
        allProducts = allProducts.filter((product: any) => {
          // 处理deptIds字段，可能是字符串或数组
          let productDeptIds: string[] = [];

          if (product.deptIds) {
            if (Array.isArray(product.deptIds)) {
              productDeptIds = product.deptIds;
            } else if (typeof product.deptIds === 'string') {
              try {
                productDeptIds = JSON.parse(product.deptIds);
              } catch (e) {
                // 如果解析失败，尝试作为单个ID处理
                productDeptIds = [product.deptIds];
              }
            }
          }

          // 检查解析后的部门ID数组
          if (productDeptIds.length > 0) {
            return productDeptIds.some((deptId: string) => allowedDeptIds.value.includes(deptId));
          }

          if (product.deptId) {
            return allowedDeptIds.value.includes(product.deptId);
          }
          // 区域用户不显示没有部门归属的产品，避免显示总部独有产品
          return false;
        });
      }

      productList.value = allProducts;
      productPagination.total = res.data.total || 0;

      // 恢复选中状态
      ensureSelectionStatus();
    }
    return Promise.resolve();
  } catch (error) {
    useMessage().error('获取产品列表失败');
    return Promise.reject(error);
  } finally {
    productListLoading.value = false;
  }
};

// 确保选中状态在分页切换时保持
const ensureSelectionStatus = () => {
  // 获取当前页码
  const currentPage = productPagination.currentPage;

  // 获取当前页应该选中的ID列表
  const currentPageShouldSelectIds = (pageSelectionMap.value[currentPage] || []).filter(
    id => selectedProductSet.value.has(id) // 确保仍在总选择集中
  );

  // 增加延迟，确保表格DOM已经完全渲染
  setTimeout(() => {
    if (!productTableRef.value) return;

    // 先清除表格所有选中状态
    productTableRef.value.clearSelection();

    // 再分别对每一行进行选中操作
    productList.value.forEach(row => {
      // 使用当前页的选中记录来判断是否应该选中该行
      if (currentPageShouldSelectIds.includes(row.code)) {
        // 选中当前行
        productTableRef.value.toggleRowSelection(row, true);
      }
    });
  }, 300); // 延长延迟时间
};

// 处理产品选择
const handleProductSelectionChange = (selection: any[]) => {
  // 获取当前页码
  const currentPage = productPagination.currentPage;

  // 记录当前页面的选中ID
  pageSelectionMap.value[currentPage] = selection.map(item => item.code);

  // 获取当前页面的所有产品code
  const currentPageProductCodes = new Set(productList.value.map(item => item.code));

  // 从selectedProductSet中移除当前页取消选中的产品
  for (const code of currentPageProductCodes) {
    // 如果当前页面有此产品但不在当前选中列表中，则表示取消选中
    const isCurrentSelected = selection.some(item => item.code === code);
    if (!isCurrentSelected) {
      selectedProductSet.value.delete(code);
      // 同时从记录的映射中删除
      delete selectedProductMap.value[code];
    }
  }

  // 添加当前页新选中的产品
  selection.forEach(product => {
    selectedProductSet.value.add(product.code);
    selectedProductMap.value[product.code] = product;
  });

  // 更新selectedProductIds
  selectedProductIds.value = Array.from(selectedProductSet.value);
};

// 确认产品选择
const confirmProductSelection = async () => {
  if (selectedProductSet.value.size === 0) {
    useMessage().error('请至少选择一个产品');
    return;
  }

  // 先清空现有数据，防止重复添加
  const existingCodes = new Set(productParam.map(item => item.code));
  const newProducts: ProductItem[] = [];
  const selectedProductCodes: string[] = [];

  // 收集所有选中的产品代码
  Object.values(selectedProductMap.value).forEach(product => {
    selectedProductCodes.push(product.code);

    const productExistsLocally = existingCodes.has(product.code);

    if (!productExistsLocally) {
      // 产品在本地推荐列表中不存在，添加到列表
      const lastSort = productParam.length > 0 ? productParam[productParam.length - 1].sort : 0;

      // 保留产品的完整 deptIds 信息，避免覆盖其他区域的选择
      let productDeptIds: string[] = [];

      // 解析产品现有的部门ID
      if (product.deptIds && Array.isArray(product.deptIds)) {
        productDeptIds = [...product.deptIds];
      } else if (product.deptIds && typeof product.deptIds === 'string') {
        try {
          productDeptIds = JSON.parse(product.deptIds);
        } catch (e) {
          productDeptIds = [product.deptIds];
        }
      } else if (product.deptId) {
        productDeptIds = [product.deptId];
      } else {
        productDeptIds = [];
      }

      // 确保当前用户的部门ID在列表中（用于显示）
      if (!productDeptIds.includes(userDeptId.value)) {
        productDeptIds.push(userDeptId.value);
      }

      newProducts.push({
        code: product.code,
        name: product.name,
        amount: product.amount || '0',
        image: product.image || '',
        deviceId: [],
        sort: lastSort + 1 + newProducts.length,
        gender: product.gender !== undefined ? Number(product.gender) : (form.gender === '男' ? 0 : 1),
        character: product.character || (form.type === 'shoes' ? convertFeatureToCharacter(form.feature) : 'normal'),
        content: product.content || [{ content: '' }],
        deptIds: productDeptIds // 保留完整的部门ID信息
      });
      existingCodes.add(product.code);
    }
  });

  // 调用后端接口建立产品访问权限（区域用户）或更新产品归属（总部用户）
  if (selectedProductCodes.length > 0) {
    try {
      if (isHeadquartersUser.value) {
        // 总部用户：可以修改产品归属
        const headquartersId = findHeadquartersId();
        if (headquartersId) {
          for (const productCode of selectedProductCodes) {
            await updateProductInfo(productCode, { deptIds: [headquartersId] });
          }
        }
        useMessage().success(`已将 ${selectedProductCodes.length} 个产品设置为总部管理`);
      } else {
        // 区域用户：建立产品访问权限，不改变产品归属
        for (const productCode of selectedProductCodes) {
          await addDeptAccess(productCode, [userDeptId.value]);
        }

        // 选择完成后，重新获取产品的最新信息以确保 deptIds 同步
        // 这样可以确保前端显示的 deptIds 包含所有区域的信息
        for (const newProduct of newProducts) {
          try {
            // 重新搜索该产品以获取最新的 deptIds
            const searchRes = await fetchShoeTypeSearch({
              code: newProduct.code,
              current: 1,
              size: 1,
              type: form.type || 'shoes'
            });

            if (searchRes.code === 0 && searchRes.data.records.length > 0) {
              const latestProduct = searchRes.data.records[0];
              // 解析最新的 deptIds
              let latestDeptIds: string[] = [];
              if (latestProduct.deptIds) {
                if (Array.isArray(latestProduct.deptIds)) {
                  latestDeptIds = [...latestProduct.deptIds];
                } else if (typeof latestProduct.deptIds === 'string') {
                  try {
                    latestDeptIds = JSON.parse(latestProduct.deptIds);
                  } catch (e) {
                    latestDeptIds = [latestProduct.deptIds];
                  }
                }
              }

              // 确保当前用户的部门ID在最新的 deptIds 中
              if (!latestDeptIds.includes(userDeptId.value)) {
                latestDeptIds.push(userDeptId.value);
              }

              // 更新本地产品的 deptIds 为合并后的数据
              newProduct.deptIds = latestDeptIds;
            }
          } catch (error) {
            // 获取产品最新信息失败，确保至少包含当前用户的部门ID
            if (!newProduct.deptIds) {
              newProduct.deptIds = [];
            }
            if (!newProduct.deptIds.includes(userDeptId.value)) {
              newProduct.deptIds.push(userDeptId.value);
            }
          }
        }

        useMessage().success(`已将 ${selectedProductCodes.length} 个产品添加到推荐列表，产品仍归属总部管理`);
      }
    } catch (error) {
      useMessage().error(isHeadquartersUser.value ? '更新产品归属失败' : '添加产品访问权限失败');
      return;
    }
  }

  // 添加新产品到本地列表
  if (newProducts.length > 0) {
    productParam.push(...newProducts);
  }

  productSelectorVisible.value = false;
};



// 编辑产品按钮点击事件
const handleEditProduct = (product: ProductItem) => {
  isNewProduct.value = false;
  const index = productParam.findIndex(item => item.code === product.code);
  if (index !== -1) {
    editingProductIndex.value = index;

    // 处理content字段 - 兼容字符串和数组两种格式
    let contentArray = [];
    if (productParam[index].content) {
      try {
        // 如果content是字符串，尝试解析为JSON
        if (typeof productParam[index].content === 'string') {
          contentArray = JSON.parse(productParam[index].content as any);
        } else if (Array.isArray(productParam[index].content)) {
          // 如果已经是数组，直接使用
          contentArray = productParam[index].content || [];
        } else {
          // 其他情况，初始化为空数组
          contentArray = [];
        }
      } catch (error) {
        // JSON解析失败时，初始化为空数组
        contentArray = [];
      }
    }

    // 确保至少有一个内容项
    if (contentArray.length === 0) {
      contentArray = [{ content: '' }];
    }

    // 复制产品数据时确保包含性别和特征字段
    productForm.value = {
      ...productParam[index],
      // 如果原产品没有这些字段，则使用默认值
      gender: productParam[index].gender || form.gender || '0',
      character: productParam[index].character ||
        (form.type === 'shoes'
          ? convertFeatureToCharacter(form.feature || '大童常规楦')
          : 'normal'),
      content: contentArray
    };
    productEditVisible.value = true;
  }
};
// 添加产品内容项
const addProductContent = () => {
  if (!productForm.value.content) {
    productForm.value.content = [];
  }

  if (productForm.value.content.length < 5) {
    productForm.value.content.push({ content: '' });
  } else {
    useMessage().wraning('最多添加5个内容项');
  }
};

// 删除产品内容项
const removeProductContent = (index: number) => {
  if (productForm.value.content && productForm.value.content.length > 1) {
    productForm.value.content.splice(index, 1);
  } else {
    useMessage().wraning('至少保留一项内容');
  }
};

// 保存产品编辑
const saveProductEdit = async () => {
  // 使用表单验证
  try {
    await productFormRef.value.validate();
  } catch (error) {
    // 验证失败时，直接返回
    return;
  }

  try {
    if (isNewProduct.value) {
      // 只有总部用户才能新增产品
      if (!isHeadquartersUser.value) {
        useMessage().error('区域用户没有新增产品的权限');
        return;
      }

      // 新增产品到后端
      const params: any = {
        code: productForm.value.code,
        name: productForm.value.name,
        image: productForm.value.image,
        amount: productForm.value.amount,
        type: form.type || 'shoes',
        gender: Number(productForm.value.gender),
        character: productForm.value.character,
        content: productForm.value.content || [{ content: '' }],
        //deptId: userDeptId.value
      };

      const res = await fetchAddProduct(params);
      if (res.code === 0) {
        // 添加到本地列表
        productParam.push({
          ...productForm.value,
          deviceId: [],
          sort: productParam.length + 1
        });
        useMessage().success('添加成功');
      } else {
        useMessage().error(res.msg || '添加失败');
        return;
      }
    } else {
      // 更新已有产品
      const res = await updateProductInfo(productForm.value.code, {
        name: productForm.value.name,
        image: productForm.value.image,
        amount: productForm.value.amount,
        gender: Number(productForm.value.gender),
        character: productForm.value.character,
        content: productForm.value.content || ''
      });
      if (res.code === 0) {
        // 更新本地列表
        if (editingProductIndex.value !== -1) {
          productParam[editingProductIndex.value] = {
            ...productForm.value
          };
        }
        useMessage().success('更新成功');
      } else {
        useMessage().error(res.msg || '更新失败');
        return;
      }
    }

    productEditVisible.value = false;
  } catch (error: any) {
    useMessage().error(error.msg || (isNewProduct.value ? '添加失败' : '更新失败'));
  }
};

// 添加特征字符串转换为数据库character值的函数
const convertFeatureToCharacter = (feature: string): string => {
  switch (feature) {
    case '大童常规楦': return 'dtRegular';
    case '大童宽楦': return 'dtWidth';
    case '大童窄楦': return 'dtNarrow';
    case '小童宽楦': return 'xtWidth';
    case '小童常规楦': return 'xtRegular';
    default: return 'normal';
  }
};

// 删除/移除产品
const deleteItempro = async (index: number) => {
  try {
    const product = productParam[index];

    // 权限检查：确保用户有权限移除该产品
    if (!canRemoveProduct(product)) {
      useMessage().error('您没有权限移除该产品');
      return;
    }

    const confirmText = isHeadquartersUser.value ? '确定要删除该产品吗？' : '确定要从推荐列表中移除该产品吗？';
    await useMessageBox().confirm(confirmText);

    // 无论是总部用户还是区域用户，都是直接从推荐列表中移除产品
    // 不涉及修改产品本身的部门归属
    productParam.splice(index, 1);

    // 更新排序
    productParam.forEach((item, idx) => {
      item.sort = idx + 1;
    });

    useMessage().success(isHeadquartersUser.value ? '产品已删除' : '产品已从推荐列表中移除');
  } catch (error) {
    // 取消删除
  }
};

// 定义校验规则
const dataRules = ref({
  title: [{ required: true, message: '标题不能为空', trigger: 'blur' }],
  gender: [{ required: true, message: '性别不能为空', trigger: 'blur' }],
  type: [{ required: true, message: '类型不能为空', trigger: 'blur' }],
  feature: [{ required: true, message: '特征不能为空', trigger: 'blur' }],
});

// 打开弹窗
const openDialog = async (id: string) => {
  visible.value = true;
  form.id = '';

  // 重置表单数据
  nextTick(() => {
    dataFormRef.value?.resetFields();
  });

  // 清空数据
  productParam.length = 0;
  introduceParam.length = 0;

  // 初始化用户权限信息
  await initUserPermissions();

  // 获取recommend信息
  if (id) {
    form.id = id;
    await getrecommendData(id);
  }
};

// 提交
const onSubmit = async () => {
  const valid = await dataFormRef.value.validate().catch(() => { });
  if (!valid) return false;

  if (productParam.length === 0) {
    useMessage().error('请至少添加一个产品');
    return false;
  }

  try {
    loading.value = true;

    // 如果是编辑模式，需要合并产品而不是覆盖
    if (form.id) {
      // 获取最新的推荐数据，确保包含所有区域的产品
      const res = await getObj(form.id);
      const latestProductParam = JSON.parse(res.data.product || '[]');

      // 创建当前区域的产品代码集合（包括移除后的状态）
      const currentProductCodes = new Set(productParam.map(p => p.code));

      // 1. 先处理所有历史产品：移除本部门ID（如果本区域没选中）
      const filteredLatestProducts = latestProductParam.map((product: any) => {
        let productDeptIds: string[] = [];
        if (product.deptIds) {
          if (Array.isArray(product.deptIds)) {
            productDeptIds = [...product.deptIds];
          } else if (typeof product.deptIds === 'string') {
            try {
              productDeptIds = JSON.parse(product.deptIds);
            } catch (e) {
              productDeptIds = [product.deptIds];
            }
          }
        }
        // 如果本区域没选中该产品，则移除本部门ID
        if (!currentProductCodes.has(product.code) && productDeptIds.includes(userDeptId.value)) {
          productDeptIds = productDeptIds.filter(id => id !== userDeptId.value);
          return { ...product, deptIds: productDeptIds };
        }
        return product;
      }).filter((product: any) => {
        // 只保留还有部门的产品
        let productDeptIds: string[] = [];
        if (product.deptIds) {
          if (Array.isArray(product.deptIds)) {
            productDeptIds = product.deptIds;
          } else if (typeof product.deptIds === 'string') {
            try {
              productDeptIds = JSON.parse(product.deptIds);
            } catch (e) {
              productDeptIds = [product.deptIds];
            }
          }
        }
        return productDeptIds.length > 0;
      });

      // 2. 只对"新选中的产品"做合并/补充本部门ID
      const latestProductMap = new Map();
      filteredLatestProducts.forEach((product: any) => {
        latestProductMap.set(product.code, product);
      });
      productParam.forEach(localProduct => {
        if (!latestProductMap.has(localProduct.code)) {
          // 新选中的产品，确保有本部门ID
          let localDeptIds: string[] = [];
          if (localProduct.deptIds) {
            if (Array.isArray(localProduct.deptIds)) {
              localDeptIds = [...localProduct.deptIds];
            } else if (typeof localProduct.deptIds === 'string') {
              try {
                localDeptIds = JSON.parse(localProduct.deptIds);
              } catch (e) {
                localDeptIds = [localProduct.deptIds];
              }
            }
          }
          if (!localDeptIds.includes(userDeptId.value)) {
            localDeptIds.push(userDeptId.value);
          }
          latestProductMap.set(localProduct.code, {
            ...localProduct,
            deptIds: localDeptIds
          });
        }
      });
      const mergedProducts = Array.from(latestProductMap.values());
      form.product = JSON.stringify(mergedProducts);
    } else {
      // 新增模式，直接使用当前产品列表
      form.product = JSON.stringify(productParam);
    }

    form.introduce = '[]';

    form.id ? await putObj(form.id, form) : await addObj(form);
    useMessage().success(form.id ? '修改成功' : '添加成功');

    visible.value = false;
    emit('refresh');

    // 重置表单验证状态
    dataFormRef.value.resetFields();
    productParam.length = 0;
    introduceParam.length = 0;
  } catch (err: any) {
    useMessage().error(err.msg);
  } finally {
    loading.value = false;
  }
};

const getrecommendData = async (id: string) => {
  loading.value = true;
  try {
    const res = await getObj(id);
    Object.assign(form, res.data);
    const parsedProductParam = JSON.parse(form.product);

    // 区域用户只显示包含自己部门ID的产品
    let filteredProducts = parsedProductParam;

    if (!isHeadquartersUser.value && allowedDeptIds.value.length > 0) {
      filteredProducts = parsedProductParam.filter((product: any) => {
        // 处理deptIds字段，可能是字符串或数组
        let productDeptIds: string[] = [];

        if (product.deptIds) {
          if (Array.isArray(product.deptIds)) {
            productDeptIds = product.deptIds;
          } else if (typeof product.deptIds === 'string') {
            try {
              productDeptIds = JSON.parse(product.deptIds);
            } catch (e) {
              // 如果解析失败，尝试作为单个ID处理
              productDeptIds = [product.deptIds];
            }
          }
        }

        // 检查解析后的部门ID数组
        if (productDeptIds.length > 0) {
          return productDeptIds.some((deptId: string) => allowedDeptIds.value.includes(deptId));
        }

        // 兼容旧的deptId字段
        if (product.deptId) {
          return allowedDeptIds.value.includes(product.deptId);
        }

        // 如果产品没有部门归属，区域用户不显示
        return false;
      });
    }

    productParam.push(...filteredProducts);

    // 仍然解析介绍列表数据，以便兼容现有数据，但不再显示在界面上
    if (form.introduce) {
      const parsedIntroduceParam = JSON.parse(form.introduce);
      introduceParam.push(...parsedIntroduceParam);
    }
  } catch (error) {
    useMessage().error('获取数据失败');
  } finally {
    loading.value = false;
  }
};

const cancel = () => {
  visible.value = false; // 关闭对话框
  dataFormRef.value.resetFields(); // 清空表单内容
  productParam.length = 0;
  introduceParam.length = 0;
};

// 重置产品搜索
const resetProductSearch = () => {
  productSearch.name = '';
  productSearch.code = '';
  productSearch.gender = '';
  productSearch.character = [];
  productPagination.currentPage = 1;
  searchProducts();
};

// 处理产品列表分页
const handleProductSizeChange = (size: number) => {
  // 先保存当前页的选中状态
  saveCurrentPageSelection();

  // 更新页面大小并重新加载数据
  productPagination.pageSize = size;
  forceRenderKey.value++;
  searchProducts();
};

const handleProductCurrentChange = (page: number) => {
  // 先保存当前页的选中状态
  saveCurrentPageSelection();

  // 更新页码并重新加载数据
  productPagination.currentPage = page;
  forceRenderKey.value++;
  searchProducts();
};

// 保存当前页面的选中状态
const saveCurrentPageSelection = () => {
  const currentPage = productPagination.currentPage;
  const currentSelection = productTableRef.value?.getSelection?.() || [];
  pageSelectionMap.value[currentPage] = currentSelection.map((item: any) => item.code);
};

// 暴露变量
defineExpose({
  openDialog
});
</script>

<style lang="scss" scoped>
.preview-image {
  height: 100px;
  width: 100px;
  margin-top: -10px;
}

.product-list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.pagination-container {
  margin-top: 15px;
  display: flex;
  justify-content: flex-end;
}

.preview-container {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  min-height: 400px;
}

.image-slot {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  background: #f5f7fa;
  color: #909399;
  font-size: 20px;
}

:deep(.preview-dialog) {
  .el-dialog__body {
    padding: 10px;
  }
}

.avatar-uploader {
  :deep() {
    .avatar {
      width: 180px;
      height: 125px;
      display: block;
    }

    .el-upload {
      border: 1px dashed var(--el-border-color);
      border-radius: 6px;
      cursor: pointer;
      position: relative;
      overflow: hidden;
      transition: var(--el-transition-duration-fast);
    }

    .el-upload:hover {
      border-color: var(--el-color-primary);
    }

    .el-icon.avatar-uploader-icon {
      font-size: 28px;
      color: #8c939d;
      width: 150px;
      height: 125px;
      text-align: center;
    }
  }
}

.device-id-item {
  margin-top: 10px;
  margin-bottom: 10px;

  :deep(.el-select) {
    width: 150px !important;

    .el-select__tags {
      max-height: 36px;
      overflow-x: hidden;
      flex-wrap: nowrap;
      white-space: nowrap;
      padding-left: 8px;
      margin-left: 4px;

      .el-tag {
        display: inline-flex;
        max-width: 80px;
        margin-right: 4px;
        margin-left: 2px;
      }

      .el-select__tags-text {
        display: inline-block;
        max-width: 60px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }

    .el-input__wrapper {
      height: 36px;
      padding: 0 8px;
    }

    .el-select__tags-collapse-tags {
      display: inline-flex;
      align-items: center;
      max-width: 60px;
      margin-left: 4px;

      .el-tag {
        display: inline-flex;
        max-width: 60px;

        .el-tag__content {
          overflow: hidden;
          text-overflow: ellipsis;
        }
      }
    }
  }
}

/* 添加样式控制表格宽度 */
.product-selector-table {
  width: 100%;
}

/* 添加搜索表单样式 */
.search-form-container {
  margin-bottom: 15px;
  width: 100%;
}

.search-form {
  width: 100%;
}

.search-form-row {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  width: 100%;
}

.search-form-buttons {
  margin-left: auto;
}

.search-form-tip {
  font-size: 15px;
  color: #999999;
}
</style>