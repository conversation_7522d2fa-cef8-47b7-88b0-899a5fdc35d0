export default {
	tenant: {
		index: '#',
		importTenantTip: ' import Tenant',
		id: 'id',
		name: 'name',
		code: 'code',
		tenantDomain: 'tenantDomain',
		startTime: 'startTime',
		endTime: 'endTime',
		status: 'status',
		delFlag: 'delFlag',
		createBy: 'createBy',
		updateBy: 'updateBy',
		createTime: 'createTime',
		updateTime: 'updateTime',
		menuId: 'menuIds',
		individuationBtn: 'individuation',
		inputidTip: 'input id',
		inputnameTip: 'input name',
		inputcodeTip: 'input code',
		inputtenantDomainTip: 'input tenantDomain',
		inputstartTimeTip: 'input startTime',
		inputendTimeTip: 'input endTime',
		inputstatusTip: 'input status',
		inputdelFlagTip: 'input delFlag',
		inputcreateByTip: 'input createBy',
		inputupdateByTip: 'input updateBy',
		inputcreateTimeTip: 'input createTime',
		inputupdateTimeTip: 'input updateTime',
		inputmenuIdTip: 'input menuId',
		deleteDisabledTip: 'base tenants are not allowed to delete',
	},
	tenantmenu: {
		name: 'tenantmenu',
		index: '#',
		status: 'status',
		createTime: 'createTime',
	},
};
