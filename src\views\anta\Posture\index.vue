<template>
  <div class="layout-padding">
    <div class="layout-padding-auto layout-padding-view">
      <el-row class="ml10" v-show="showSearch">
        <el-form :inline="true" :model="state.queryForm" @keyup.enter="getDataList" ref="queryRef">
          <el-form-item>
            <el-input placeholder="姓名" style="max-width: 180px" v-model.trim="state.queryForm.name" />
          </el-form-item>
          <el-form-item>
            <el-input placeholder="门店" style="max-width: 180px" v-model.trim="state.queryForm.storeName" />
          </el-form-item>
          <el-form-item>
            <el-date-picker v-model="state.queryForm.dateRange" type="daterange" range-separator="至"
              start-placeholder="开始日期" end-placeholder="结束日期" style="max-width: 300px" value-format="YYYY-MM-DD" />
          </el-form-item>
          <el-form-item>
            <el-button @click="handleQuery" icon="search" type="primary">
              {{ $t('common.queryBtn') }}
            </el-button>
            <el-button @click="resetQuery" icon="Refresh">{{ $t('common.resetBtn') }}</el-button>
          </el-form-item>
        </el-form>
      </el-row>
      <el-row>
        <div class="mb8" style="width: 100%">
          <el-button @click="formDialogRef.openDialog()" class="ml10" icon="folder-add" type="primary">
            {{ $t('common.addBtn') }}
          </el-button>
          <!-- <el-button plain @click="excelUploadRef.show()" class="ml10" icon="upload-filled" type="primary">
            {{ $t('common.importBtn') }}
          </el-button> -->
          <el-button plain :disabled="multiple" @click="handleDelete(selectObjs)" class="ml10" icon="Delete"
            type="primary">
            {{ $t('common.delBtn') }}
          </el-button>
          <right-toolbar :export="'sys_post_export'" @exportExcel="exportExcel" @queryTable="getDataList" class="ml10"
            style="float: right; margin-right: 20px" v-model:showSearch="showSearch"></right-toolbar>
        </div>
      </el-row>

      <el-table :data="state.dataList" v-loading="state.loading" border :cell-style="tableStyle.cellStyle"
        :header-cell-style="tableStyle.headerCellStyle" @selection-change="selectionChangeHandle"
        @sort-change="sortChangeHandle">
        <el-table-column type="selection" width="40" align="center" />
        <el-table-column type="index" label="#" width="45" />
        <el-table-column prop="deviceCode" label="设备编号" show-overflow-tooltip width="100" />
        <el-table-column prop="storeName" label="门店" show-overflow-tooltip width="100" />
        <el-table-column prop="name" label="名字" show-overflow-tooltip />
        <el-table-column prop="age" label="年龄" show-overflow-tooltip />
        <el-table-column prop="gender" label="性别" show-overflow-tooltip />
        <el-table-column prop="createTime" label="创建时间" show-overflow-tooltip width="100" />
        <el-table-column prop="shoulderAngle" label="肩角(°)" show-overflow-tooltip />
        <!-- <el-table-column prop="shoulderLow" label="头(°)"  show-overflow-tooltip/>
          <el-table-column prop="shoulderHigh" label="头高(cm)"  show-overflow-tooltip/> -->
        <el-table-column prop="leftLegLength" label="左腿长(cm)" show-overflow-tooltip width="100" />
        <el-table-column prop="rightLegLength" label="右腿长(cm)" show-overflow-tooltip width="100" />
        <el-table-column prop="leftKneeAngle" label="左膝角(°)" show-overflow-tooltip width="100" />
        <el-table-column prop="rightKneeAngle" label="右膝角(°)" show-overflow-tooltip width="100" />
        <el-table-column prop="ankleDistance" label="踝间距(mm)" show-overflow-tooltip width="110" />
        <el-table-column prop="headForward" label="头前伸距离(mm)" show-overflow-tooltip width="130" />
        <el-table-column prop="shoulderHipAngle" label="肩臀角(°)" show-overflow-tooltip width="100" />
        <!-- <el-table-column prop="backTop" label="背面上半身"  show-overflow-tooltip width="100"/>
           <el-table-column prop="video" label="视频"  show-overflow-tooltip/> 
          <el-table-column prop="boneData" label="骨骼数据"  show-overflow-tooltip width="100"/> 
       <el-table-column prop="sideTop" label="侧面上半身"  show-overflow-tooltip width="100"/>
           <el-table-column prop="frontData" label=" frontData"  show-overflow-tooltip/>
          <el-table-column prop="sideData" label=" sideData"  show-overflow-tooltip/>
          <el-table-column prop="motionData" label=" motionData"  show-overflow-tooltip/> 
          <el-table-column prop="deviceCode" label="设备编码"  show-overflow-tooltip/> 
           <el-table-column prop="leftStrides" label="左步幅"  show-overflow-tooltip/>
          <el-table-column prop="rightStrides" label="右步幅"  show-overflow-tooltip/>
          <el-table-column prop="stepSpeed" label="步速（米/分）"  show-overflow-tooltip width="120"/> -->
        <el-table-column prop="leftStepLength" label="左步长(cm)" show-overflow-tooltip width="100" />
        <el-table-column prop="rightStepLength" label="右步长(cm)" show-overflow-tooltip width="100" />
        <el-table-column prop="kneesDistance" label="膝间距(mm)" show-overflow-tooltip width="110" />
        <!-- <el-table-column prop="leftStepWidth" label="左步宽"  show-overflow-tooltip/>
          <el-table-column prop="rightStepWidth" label="右步宽"  show-overflow-tooltip/> 
          <el-table-column prop="stepFrequency" label="步频（步/分）"  show-overflow-tooltip width="120"/> 
           <el-table-column prop="kneeAngleSide" label="侧膝角"  show-overflow-tooltip/>
          <el-table-column prop="leftFootAngle" label="左足角"  show-overflow-tooltip/>
          <el-table-column prop="rightFootAngle" label="右足角"  show-overflow-tooltip/>
          <el-table-column prop="eyesDistance" label="眼间距"  show-overflow-tooltip/>
          <el-table-column prop="leftEyeWidth" label="左眼宽"  show-overflow-tooltip/>
          <el-table-column prop="rightEyeWidth" label="右眼宽"  show-overflow-tooltip/>
          <el-table-column prop="headWidth" label="头宽"  show-overflow-tooltip/>
          <el-table-column prop="leftArmLenght" label="左臂长"  show-overflow-tooltip/>
          <el-table-column prop="rightArmLength" label="右臂长"  show-overflow-tooltip/>
          <el-table-column prop="bodyLength" label="躯干长"  show-overflow-tooltip/>
          <el-table-column prop="shoulderWidth" label="肩宽"  show-overflow-tooltip/>  -->
        <!-- <el-table-column label="操作" width="200">
          <template #default="scope">
            <el-button text type="primary" icon="view" v-auth="'sys_role_edit'"
              @click="formDialogRef.openDialog(scope.row.measureId, true)">
              详情
            </el-button>
            <el-button icon="edit-pen" text type="primary" v-auth="'anta_Posture_edit'"
              @click="formDialogRef.openDialog(scope.row.measureId)">编辑</el-button>
            <el-button icon="delete" text type="primary" v-auth="'anta_Posture_del'"
              @click="handleDelete([scope.row.measureId])">
              删除
            </el-button>
          </template>
</el-table-column> -->
      </el-table>
      <pagination @size-change="sizeChangeHandle" @current-change="currentChangeHandle" v-bind="state.pagination" />
    </div>

    <!-- 编辑、新增  -->
    <form-dialog ref="formDialogRef" @refresh="getDataList(false)" />
  </div>
</template>

<script setup lang="ts" name="systemPosture">
import { BasicTableProps, useTable } from "/@/hooks/table";
import { fetchList, delObjs } from "/@/api/anta/Posture";
import { useMessage, useMessageBox } from "/@/hooks/message";
import { useDict } from '/@/hooks/dict';
// 引入组件
const FormDialog = defineAsyncComponent(() => import('./form.vue'));

// 定义查询字典

// 定义变量内容
const formDialogRef = ref()
const excelUploadRef = ref();
// 搜索变量
const queryRef = ref()
const showSearch = ref(true)
// 多选变量
const selectObjs = ref([]) as any
const multiple = ref(true)

const initialQueryForm = {
  name: '',
  deviceCode: '',
  dateRange: [],
  storeName: ''
}
const state: BasicTableProps = reactive<BasicTableProps>({
  queryForm: { ...initialQueryForm },
  pageList: fetchList
})

//  table hook
const {
  getDataList,
  currentChangeHandle,
  sizeChangeHandle,
  sortChangeHandle,
  downBlobFile,
  tableStyle
} = useTable(state)

// 查询参数处理
const handleQuery = () => {
  let startTime = ''
  let endTime = ''
  if (
    Array.isArray(state.queryForm.dateRange) &&
    state.queryForm.dateRange.length === 2 &&
    /^\d{4}-\d{2}-\d{2}$/.test(state.queryForm.dateRange[0]) &&
    /^\d{4}-\d{2}-\d{2}$/.test(state.queryForm.dateRange[1])
  ) {
    startTime = state.queryForm.dateRange[0]
    endTime = state.queryForm.dateRange[1]
  }
  const params = {
    name: state.queryForm.name,
    deviceCode: state.queryForm.deviceCode,
    storeName: state.queryForm.storeName,
    startTime,
    endTime
  }
  fetchList(params).then(res => {
    state.dataList = res.data.records
    state.pagination && (state.pagination.total = res.data.total)
  })
}

// 清空搜索条件
const resetQuery = () => {
  Object.assign(state.queryForm, initialQueryForm)
  state.queryForm.startTime = ''
  state.queryForm.endTime = ''
  state.queryForm.storeName = ''
  selectObjs.value = []
  queryRef.value && queryRef.value.resetFields && queryRef.value.resetFields()
  getDataList()
}

const exportExcel = () => {
  // 拆分时间范围
  let startTime = ''
  let endTime = ''
  if (
    Array.isArray(state.queryForm.dateRange) &&
    state.queryForm.dateRange.length === 2 &&
    /^\d{4}-\d{2}-\d{2}$/.test(state.queryForm.dateRange[0]) &&
    /^\d{4}-\d{2}-\d{2}$/.test(state.queryForm.dateRange[1])
  ) {
    startTime = state.queryForm.dateRange[0]
    endTime = state.queryForm.dateRange[1]
  }
  const params = {
    name: state.queryForm.name,
    deviceCode: state.queryForm.deviceCode,
    startTime,
    endTime
  }
  downBlobFile('/anta/footShapeData/export', params, '体态数据.xlsx')
}

// 多选事件
const selectionChangeHandle = (objs: { measureId: string }[]) => {
  selectObjs.value = objs.map(({ measureId }) => measureId);
  multiple.value = !objs.length;
};

// 删除操作
const handleDelete = async (ids: string[]) => {
  try {
    await useMessageBox().confirm('此操作将永久删除');
  } catch {
    return;
  }

  try {
    await delObjs(ids);
    getDataList();
    useMessage().success('删除成功');
  } catch (err: any) {
    useMessage().error(err.msg);
  }
};
</script>