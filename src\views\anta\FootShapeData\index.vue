<template>
  <div class="layout-padding">
    <div class="layout-padding-auto layout-padding-view">
      <el-row class="ml10" v-show="showSearch">
        <el-form :inline="true" :model="state.queryForm" @keyup.enter="handleQuery" ref="queryRef">
          <el-form-item>
            <el-input placeholder="姓名" style="max-width: 180px" v-model.trim="state.queryForm.name" />
          </el-form-item>
          <el-form-item>
            <el-input placeholder="门店" style="max-width: 180px" v-model.trim="state.queryForm.storeName" />
          </el-form-item>
          <el-form-item>
            <el-date-picker v-model="state.queryForm.dateRange" type="daterange" range-separator="至"
              start-placeholder="开始日期" end-placeholder="结束日期" style="max-width: 300px" value-format="YYYY-MM-DD" />
          </el-form-item>
          <el-form-item>
            <el-button @click="handleQuery" icon="search" type="primary">
              {{ $t('common.queryBtn') }}
            </el-button>
            <el-button @click="resetQuery" icon="Refresh">{{ $t('common.resetBtn') }}</el-button>
          </el-form-item>
        </el-form>
      </el-row>
      <el-row>
        <div class="mb8" style="width: 100%">
          <el-button @click="formDialogRef.openDialog()" class="ml10" icon="folder-add" type="primary">
            {{ $t('common.addBtn') }}
          </el-button>
          <!-- <el-button plain @click="excelUploadRef.show()" class="ml10" icon="upload-filled" type="primary">
            {{ $t('common.importBtn') }}
          </el-button> -->
          <el-button plain :disabled="multiple" @click="handleDelete(selectObjs)" class="ml10" icon="Delete"
            type="primary">
            {{ $t('common.delBtn') }}
          </el-button>
          <right-toolbar :export="'sys_post_export'" @exportExcel="exportExcel" @queryTable="handleQuery" class="ml10"
            style="float: right; margin-right: 20px" v-model:showSearch="showSearch"></right-toolbar>
        </div>
      </el-row>

      <el-table :data="state.dataList" v-loading="state.loading" border :cell-style="tableStyle.cellStyle"
        :header-cell-style="tableStyle.headerCellStyle" @selection-change="selectionChangeHandle"
        @sort-change="sortChangeHandle">
        <el-table-column type="selection" width="40" align="center" />
        <el-table-column type="index" label="#" width="45" />
        <el-table-column prop="deviceCode" label="设备编号" show-overflow-tooltip width="100" />
        <el-table-column prop="storeName" label="门店" show-overflow-tooltip width="100" />
        <el-table-column prop="name" label="姓名" show-overflow-tooltip />
        <el-table-column prop="age" label="年龄" show-overflow-tooltip />
        <el-table-column prop="gender" label="性别" show-overflow-tooltip />
        <el-table-column prop="createTime" label="创建时间" show-overflow-tooltip width="100" />
        <el-table-column prop="leftFootType" label="左足型(mm)" show-overflow-tooltip width="110" />
        <el-table-column prop="rightFootType" label="右足型(mm)" show-overflow-tooltip width="110" />
        <el-table-column prop="leftFootLength" label="左足长(mm)" show-overflow-tooltip width="110" />
        <el-table-column prop="rightFootLength" label="右足长(mm)" show-overflow-tooltip width="110" />
        <el-table-column prop="leftFootWidth" label="左足宽(mm)" show-overflow-tooltip width="110" />
        <el-table-column prop="rightFootWidth" label="右足宽(mm)" show-overflow-tooltip width="110" />
        <el-table-column prop="leftRearfootWidth" label="左足后跟宽(mm)" show-overflow-tooltip width="130" />
        <el-table-column prop="rightRearfootWidth" label="右足后跟宽(mm)" show-overflow-tooltip width="130" />
        <el-table-column prop="leftFootarch" label="左足弓系数" show-overflow-tooltip width="130" />
        <el-table-column prop="rightFootarch" label="右足弓系数" show-overflow-tooltip width="130" />

        <el-table-column prop="leftFootarchLength" label="左足弓长(mm)" show-overflow-tooltip width="130" />
        <el-table-column prop="rightFootarchLength" label="右足弓长(mm)" show-overflow-tooltip width="130" />
        <el-table-column prop="leftFootarchHeight" label="左足弓高(mm)" show-overflow-tooltip width="130" />
        <el-table-column prop="rightFootarchHeight" label="右足弓高(mm)" show-overflow-tooltip width="130" />

        <el-table-column prop="leftFirstToeAngle" label="左大拇指外翻(°)" show-overflow-tooltip width="130" />
        <el-table-column prop="rightFirstToeAngle" label="右大拇指外翻(°)" show-overflow-tooltip width="130" />
        <el-table-column prop="leftLittleToeAngle" label="左小指外翻(°)" show-overflow-tooltip width="130" />
        <el-table-column prop="rightLittleToeAngle" label="右小指外翻(°)" show-overflow-tooltip width="130" />
        <el-table-column prop="leftFirstToeHeight" label="左拇指高(mm)" show-overflow-tooltip width="130" />
        <el-table-column prop="rightFirstToeHeight" label="右拇指高(mm)" show-overflow-tooltip width="130" />
        <el-table-column prop="leftFootHeight" label=" 左脚背高(mm)" show-overflow-tooltip width="130" />
        <el-table-column prop="rightFootHeight" label=" 右脚背高(mm)" show-overflow-tooltip width="130" />
        <!-- <el-table-column prop="leftToeWidth" label="左趾宽"  show-overflow-tooltip/>
          <el-table-column prop="rightToeWidth" label="右趾宽"  show-overflow-tooltip/>
          <el-table-column prop="leftArchHeight" label=" leftArchHeight"  show-overflow-tooltip/>
          <el-table-column prop="rightArchHeight" label=" rightArchHeight"  show-overflow-tooltip/>
          <el-table-column prop="leftFirstToeHeight" label=" leftFirstToeHeight"  show-overflow-tooltip/>
          <el-table-column prop="rightFirstToeHeight" label=" rightFirstToeHeight"  show-overflow-tooltip/>
          <el-table-column prop="leftLengthWidthRate" label="左足长宽比"  show-overflow-tooltip/>
          <el-table-column prop="rightLengthWidthRate" label=" rightLengthWidthRate"  show-overflow-tooltip/>
          <el-table-column prop="leftFootPrint" label="左足印图"  show-overflow-tooltip/>
          <el-table-column prop="rightFootPrint" label=" rightFootPrint"  show-overflow-tooltip/>
          <el-table-column prop="leftFootPrintOrigin" label="左足印原始图"  show-overflow-tooltip/>
          <el-table-column prop="rightFootPrintOrigin" label="右足印原始图"  show-overflow-tooltip/> -->

        <!-- 
        <el-table-column label="操作" width="200">
          <template #default="scope">
            <el-button text type="primary" icon="view" v-auth="'sys_role_edit'" @click="formDialogRef.openDialog(scope.row.id, true)">
              详情
            </el-button>
            <el-button icon="edit-pen" text type="primary" v-auth="'anta_FootShapeData_edit'"
              @click="formDialogRef.openDialog(scope.row.id)">编辑</el-button>
            <el-button icon="delete" text type="primary" v-auth="'anta_FootShapeData_del'" @click="handleDelete([scope.row.id])">
              删除
            </el-button>
          </template>
</el-table-column> -->
      </el-table>
      <pagination @size-change="sizeChangeHandle" @current-change="currentChangeHandle" v-bind="state.pagination" />
    </div>

    <!-- 编辑、新增  -->
    <form-dialog ref="formDialogRef" @refresh="handleQuery" />
  </div>
</template>

<script setup lang="ts" name="systemFootShapeData">
import { BasicTableProps, useTable } from "/@/hooks/table";
import { fetchList, delObjs } from "/@/api/anta/FootShapeData";
import { useMessage, useMessageBox } from "/@/hooks/message";
// 引入组件
const FormDialog = defineAsyncComponent(() => import('./form.vue'));

// const formatValue = (value: string | number | null): string => {
//   return value ? String(value) : '-';
// };

// 定义查询字典

// 定义变量内容
const formDialogRef = ref()
const excelUploadRef = ref();
// 搜索变量
const queryRef = ref()
const showSearch = ref(true)
// 多选变量
const selectObjs = ref([]) as any
const multiple = ref(true)

const initialQueryForm = {
  name: '',
  deviceCode: '',
  dateRange: [],
  storeName: ''
}
const state: BasicTableProps = reactive<BasicTableProps>({
  queryForm: { ...initialQueryForm },
  pageList: fetchList
})

//  table hook
const {
  getDataList,
  currentChangeHandle,
  sizeChangeHandle,
  sortChangeHandle,
  downBlobFile,
  tableStyle
} = useTable(state)

// 查询参数处理
const handleQuery = () => {
  const [startTime, endTime] = Array.isArray(state.queryForm.dateRange)
    ? state.queryForm.dateRange
    : ['', '']
  const params = {
    name: state.queryForm.name,
    deviceCode: state.queryForm.deviceCode,
    storeName: state.queryForm.storeName,
    startTime: startTime || '',
    endTime: endTime || ''
  }
  fetchList(params).then(res => {
    state.dataList = res.data.records
    state.pagination && (state.pagination.total = res.data.total)
  })
}

// 清空搜索条件
const resetQuery = () => {
  Object.assign(state.queryForm, initialQueryForm)
  state.queryForm.startTime = ''
  state.queryForm.endTime = ''
  state.queryForm.storeName = ''
  selectObjs.value = []
  queryRef.value && queryRef.value.resetFields && queryRef.value.resetFields()
  getDataList()
}

// 导出excel
const exportExcel = () => {
  // 拆分时间范围
  let startTime = ''
  let endTime = ''
  if (
    Array.isArray(state.queryForm.dateRange) &&
    state.queryForm.dateRange.length === 2 &&
    /^\d{4}-\d{2}-\d{2}$/.test(state.queryForm.dateRange[0]) &&
    /^\d{4}-\d{2}-\d{2}$/.test(state.queryForm.dateRange[1])
  ) {
    startTime = state.queryForm.dateRange[0]
    endTime = state.queryForm.dateRange[1]
  }
  const params = {
    name: state.queryForm.name,
    deviceCode: state.queryForm.deviceCode,
    startTime,
    endTime
  }
  downBlobFile('/anta/footShapeData/export', params, '脚型数据.xlsx')
}

// 多选事件
const selectionChangeHandle = (objs: { measureId: string }[]) => {
  selectObjs.value = objs.map(({ measureId }) => measureId);
  multiple.value = !objs.length;
};

// 删除操作
const handleDelete = async (ids: string[]) => {
  try {
    await useMessageBox().confirm('此操作将永久删除');
  } catch {
    return;
  }

  try {
    await delObjs(ids);
    getDataList();
    useMessage().success('删除成功');
  } catch (err: any) {
    useMessage().error(err.msg);
  }
};
</script>