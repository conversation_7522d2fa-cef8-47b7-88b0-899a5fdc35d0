<template>
  <div class="layout-padding">
    <div class="layout-padding-auto layout-padding-view">
      <!-- 添加搜索表单 -->
      <el-form :model="state.queryForm" ref="queryRef" :inline="true" class="mb15">
        <el-form-item label="商品名称" prop="name">
          <el-input v-model="state.queryForm.name" placeholder="请输入商品名称" clearable style="width: 200px"
            @keyup.enter="handleQuery" />
        </el-form-item>
        <el-form-item label="商品编码" prop="code">
          <el-input v-model="state.queryForm.code" placeholder="请输入商品编码" clearable style="width: 200px"
            @keyup.enter="handleQuery" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
          <el-button icon="Refresh" @click="handleReset">重置</el-button>
        </el-form-item>
        <el-form-item v-if="userDeviceList.length > 1" label="当前用户" prop="currentUser">
          <el-select v-model="currentUsername" placeholder="请选择用户" @change="handleUserChange">
            <el-option v-for="item in userDeviceList" :key="item.deviceId" :label="item.username"
              :value="item.username" />
          </el-select>
        </el-form-item>
      </el-form>
      <!-- <el-row>
        <div class="mb8" style="width: 100%">
          <el-button icon="folder-add" type="primary" class="ml10" @click="formDialogRef.openDialog()">
            新 增
          </el-button>
          <el-button plain :disabled="multiple" icon="Delete" type="primary" @click="handleDelete(selectObjs)">
            删除
          </el-button>
          <right-toolbar v-model:showSearch="showSearch" :export="'anta_recommend_export'" @exportExcel="exportExcel"
            class="ml10 mr20" style="float: right;" @queryTable="getDataList"></right-toolbar>
        </div>
      </el-row> -->
      <el-table :data="state.dataList" v-loading="state.loading" border :cell-style="tableStyle.cellStyle"
        :header-cell-style="tableStyle.headerCellStyle" @selection-change="selectionChangHandle"
        @sort-change="sortChangeHandle">
        <el-table-column type="selection" width="40" align="center" />
        <el-table-column type="index" label="id" width="50" />
        <el-table-column prop="name" label="商品名称" show-overflow-tooltip />
        <el-table-column prop="image" label="商品图片" width="120">
          <template #default="scope">
            <el-image :src="scope.row.image" fit="contain" style="width: 80px; height: 80px; cursor: pointer;"
              @click="handlePreviewImage(scope.row.image)">
              <template #error>
                <div class="image-slot">
                  <el-icon>
                    <Picture />
                  </el-icon>
                </div>
              </template>
            </el-image>
          </template>
        </el-table-column>
        <el-table-column prop="code" label="商品编码" show-overflow-tooltip />
        <el-table-column prop="amount" label="商品价格" show-overflow-tooltip />

        <el-table-column v-if="userDeviceIds.length > 1" label="设备状态" width="200">
          <template #default="scope">
            <div class="device-status">
              <!-- 最多显示前4个设备，其余用省略号表示 -->
              <el-tag v-for="deviceId in userDeviceIds.slice(0, 4)" :key="deviceId"
                :type="getDeviceStatusType(scope.row, deviceId)" size="small" class="device-tag"
                :title="`${getDeviceDisplayName(scope.row, deviceId)}: ${getDeviceStatusText(scope.row, deviceId)}`">
                {{ getShortDeviceName(scope.row, deviceId) }}: {{ getDeviceStatusText(scope.row, deviceId) }}
              </el-tag>
              <!-- 如果设备超过4个，显示省略提示 -->
              <el-popover v-if="userDeviceIds.length > 4" placement="top" :width="300" trigger="hover">
                <template #reference>
                  <el-tag size="small" type="info" class="device-tag">
                    +{{ userDeviceIds.length - 4 }}个设备...
                  </el-tag>
                </template>
                <!-- 弹出框显示剩余设备状态 -->
                <div class="device-status-popover">
                  <el-tag v-for="deviceId in userDeviceIds.slice(4)" :key="deviceId"
                    :type="getDeviceStatusType(scope.row, deviceId)" size="small" class="device-tag"
                    :title="`${getDeviceDisplayName(scope.row, deviceId)}: ${getDeviceStatusText(scope.row, deviceId)}`">
                    {{ getShortDeviceName(scope.row, deviceId) }}: {{ getDeviceStatusText(scope.row, deviceId) }}
                  </el-tag>
                </div>
              </el-popover>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="库存操作" :width="isStoreUser ? 120 : 250">
          <template #default="scope">
            <el-button :type="getButtonProps(scope.row).type" size="small" @click="handleDeviceBinding(scope.row)">
              {{ getButtonProps(scope.row).text }}
            </el-button>
            <!-- 只有非门店用户才显示全部上下架按钮 -->
            <template v-if="!isStoreUser && userDeviceIds.length > 1">
              <el-button type="danger" size="small" @click="handleBatchOutOfStockForProduct(scope.row)" class="ml5">
                全部下架
              </el-button>
              <el-button type="success" size="small" @click="handleBatchRestockForProduct(scope.row)" class="ml5">
                全部补货
              </el-button>
            </template>
          </template>
        </el-table-column>
      </el-table>
      <pagination @size-change="sizeChangeHandle" @current-change="currentChangeHandle" v-bind="state.pagination" />
    </div>

    <!-- 添加图片预览对话框 -->
    <el-dialog v-model="previewVisible" width="800px" :modal="true" :append-to-body="true" :close-on-click-modal="true"
      :show-close="true" class="preview-dialog" :z-index="3000">
      <div class="preview-container">
        <img :src="previewImage" style="max-width: 100%; max-height: 80vh; object-fit: contain;">
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts" name="systemRecommend">
import { ref, reactive, onMounted } from 'vue'
import { Picture } from '@element-plus/icons-vue'
import { BasicTableProps, useTable } from "/@/hooks/table";
import { fetchListProducts, fetchUpdateDeviceId, fetchSearch, fetchSingleDeviceUsers } from "/@/api/anta/recommend";
import { useMessage, useMessageBox } from "/@/hooks/message";
import { useUserInfo } from '/@/stores/userInfo';
import { deptTree } from '/@/api/admin/dept';

// 定义变量内容
// 搜索变量
const queryRef = ref()
// 多选变量
const selectObjs = ref([]) as any
const multiple = ref(true)

// 添加设备ID和部门ID相关的状态
const currentDeviceId = ref('')
const currentDeptId = ref('')
const userDeviceIds = ref<string[]>([])
const isStoreUser = ref(false) // 是否为门店用户
const globalDeviceUserMap = ref<Map<string, string>>(new Map()) // 全局设备ID到用户名的映射
const currentUsername = ref('') // 当前选择的用户名
const userDeviceList = ref<Array<{ deviceId: string, username: string }>>([]) // 用户设备列表

// 先获取用户信息，避免初始化时闪现全部数据
const data = useUserInfo().userInfos;
currentDeviceId.value = data.user.deviceId
currentDeptId.value = data.user.deptId

// 获取部门树数据来判断用户类型
const getDeptData = async () => {
  try {
    const res = await deptTree()
    const deptData = res.data

    // 查找当前用户的部门信息
    const findDeptById = (depts: any[], deptId: string): any => {
      for (const dept of depts) {
        if (dept.id === deptId) {
          return dept
        }
        if (dept.children && dept.children.length > 0) {
          const found = findDeptById(dept.children, deptId)
          if (found) return found
        }
      }
      return null
    }

    const currentDept = findDeptById(deptData, currentDeptId.value)
    // 判断是否为门店用户：部门名称包含"门店"
    isStoreUser.value = currentDept?.name?.includes('门店') || false

  } catch (error) {
    // 获取部门数据失败
    // 如果获取失败，暂时假设不是门店用户
    isStoreUser.value = false
  }
}

// 调用部门数据获取
getDeptData()

// 获取单设备用户列表
const getSingleDeviceUsers = async () => {
  try {
    const res = await fetchSingleDeviceUsers()
    if (res.code === 0 && res.data && Array.isArray(res.data)) {
      // 过滤出当前用户拥有的设备
      const filteredUsers = res.data.filter((user: any) =>
        userDeviceIds.value.includes(user.deviceId)
      )

      if (filteredUsers.length > 0) {
        // 更新用户设备列表
        userDeviceList.value = filteredUsers.map((user: any) => ({
          deviceId: user.deviceId,
          username: user.username
        }))

        // 更新全局设备用户映射
        filteredUsers.forEach((user: any) => {
          globalDeviceUserMap.value.set(user.deviceId, user.username)
        })

        // 设置默认选择的用户名
        if (userDeviceList.value.length > 0) {
          const defaultUser = userDeviceList.value.find(item => item.deviceId === currentDeviceId.value)
          currentUsername.value = defaultUser?.username || userDeviceList.value[0].username
        }
      }
    }
  } catch (error) {
    // 获取单设备用户列表失败，使用设备ID作为占位符
  }
}

// 调用获取单设备用户列表
getSingleDeviceUsers()

// 处理用户的设备ID列表
if (data.user.deviceId) {
  // 如果deviceId是逗号分隔的字符串，拆分为数组
  if (typeof data.user.deviceId === 'string' && data.user.deviceId.includes(',')) {
    userDeviceIds.value = data.user.deviceId.split(',').filter(Boolean)
    // 使用第一个设备ID作为默认的当前设备ID
    currentDeviceId.value = userDeviceIds.value[0] || ''
  } else {
    userDeviceIds.value = [data.user.deviceId]
    currentDeviceId.value = data.user.deviceId
  }

  // 用户设备列表将通过API获取，这里不需要初始化
} else {
  userDeviceIds.value = []
}

const state: BasicTableProps = reactive<BasicTableProps>({
  queryForm: {
    name: ''
  },
  pageList: fetchListProducts,
  createdIsNeed: false  // 禁用自动加载，避免在部门ID设置前加载全部数据
})

//  table hook
const {
  getDataList: originalGetDataList,
  currentChangeHandle: originalCurrentChangeHandle,
  sizeChangeHandle: originalSizeChangeHandle,
  sortChangeHandle,
  tableStyle
} = useTable(state)

// 重写分页处理函数以确保参数传递
const currentChangeHandle = (val: number) => {
  setPageListWithParams() // 确保参数是最新的
  originalCurrentChangeHandle(val)
}

const sizeChangeHandle = (val: number) => {
  setPageListWithParams() // 确保参数是最新的
  originalSizeChangeHandle(val)
}

// 在组件挂载时初始化数据
onMounted(() => {
  getDataList()
})

// 永久设置pageList函数以包含额外参数
const setPageListWithParams = () => {
  const extraParams = {
    type: 'shoes',
    code: state.queryForm.code,
    deptId: currentDeptId.value
  }

  state.pageList = (params: any) => {
    return fetchListProducts({ ...params, ...extraParams })
  }
}

// 初始设置pageList函数
setPageListWithParams()

// 重写 getDataList 方法，传递部门ID参数
const getDataList = async (isSearch?: boolean) => {
  // 确保pageList函数包含最新的参数
  setPageListWithParams()

  // 设置额外的查询参数
  const extraParams = {
    type: 'shoes',
    name: state.queryForm.name,
    deptId: currentDeptId.value,
    code: state.queryForm.code
  }

  // 如果是搜索模式且有搜索条件，使用搜索接口
  if (isSearch && (state.queryForm.name || state.queryForm.code)) {
    state.loading = true;
    try {
      const searchParams = {
        ...state.queryForm,
        ...extraParams,
        current: state.pagination?.current || 1,
        size: state.pagination?.size || 10
      }
      const res = await fetchSearch(searchParams);
      if (res.code === 0) {
        state.dataList = res.data.records || [];
        if (state.pagination) {
          state.pagination.total = res.data.total || 0;
        }

        // 更新全局设备用户映射（用于补充信息）
        if (state.dataList) {
          state.dataList.forEach((item: any) => {
            if (item.stockStatus && Array.isArray(item.stockStatus)) {
              item.stockStatus.forEach((status: any) => {
                if (status.deviceId && status.username) {
                  globalDeviceUserMap.value.set(status.deviceId, status.username)
                }
              })
            }
          })
        }

        // 为每个商品添加 _inStock 属性
        if (state.dataList) {
          state.dataList.forEach((item: any) => {
            // 使用 defineProperty 来添加响应式属性
            if (!item.hasOwnProperty('_inStock')) {
              Object.defineProperty(item, '_inStock', {
                get: () => {
                  // 如果 deviceId 不存在或不是数组，说明没有无货记录，返回 true（有货）
                  if (!item.deviceId || !Array.isArray(item.deviceId)) {
                    return true
                  }
                  // 检查用户的任何一个设备ID是否在商品的deviceId数组中
                  // 如果用户的任何设备都在无货列表中，返回 false（无货）
                  // 否则返回 true（有货）
                  const hasAnyDeviceInStock = userDeviceIds.value.some(userDeviceId =>
                    !item.deviceId.includes(userDeviceId)
                  )
                  return hasAnyDeviceInStock
                },
                set: (value: boolean) => {
                  const deviceIds = Array.isArray(item.deviceId) ? [...item.deviceId] : []
                  if (value) {
                    // 当设置为有货时，从 deviceId 数组中移除当前设备ID
                    item.deviceId = deviceIds.filter((id: string) => id !== currentDeviceId.value)
                  } else {
                    // 当设置为无货时，向 deviceId 数组中添加当前设备ID
                    if (!deviceIds.includes(currentDeviceId.value)) {
                      deviceIds.push(currentDeviceId.value)
                    }
                    item.deviceId = deviceIds
                  }
                },
                configurable: true
              })
            }
          })
        }
      }
    } catch (error) {
      useMessage().error('查询失败');
    } finally {
      state.loading = false;
    }
    return
  }

  const res = await originalGetDataList(isSearch)

  // 更新全局设备用户映射（用于补充信息）
  if (state.dataList) {
    state.dataList.forEach((item: any) => {
      if (item.stockStatus && Array.isArray(item.stockStatus)) {
        item.stockStatus.forEach((status: any) => {
          if (status.deviceId && status.username) {
            globalDeviceUserMap.value.set(status.deviceId, status.username)
          }
        })
      }
    })
  }

  // 为每个商品添加 _inStock 属性
  if (state.dataList) {
    state.dataList.forEach((item: any) => {
      // 使用 defineProperty 来添加响应式属性
      if (!item.hasOwnProperty('_inStock')) {
        Object.defineProperty(item, '_inStock', {
          get: () => {
            // 如果 deviceId 不存在或不是数组，说明没有无货记录，返回 true（有货）
            if (!item.deviceId || !Array.isArray(item.deviceId)) {
              return true
            }
            // 检查用户的任何一个设备ID是否在商品的deviceId数组中
            // 如果用户的任何设备都在无货列表中，返回 false（无货）
            // 否则返回 true（有货）
            const hasAnyDeviceInStock = userDeviceIds.value.some(userDeviceId =>
              !item.deviceId.includes(userDeviceId)
            )
            return hasAnyDeviceInStock
          },
          set: (value: boolean) => {
            const deviceIds = Array.isArray(item.deviceId) ? [...item.deviceId] : []
            if (value) {
              // 当设置为有货时，从 deviceId 数组中移除当前设备ID
              item.deviceId = deviceIds.filter((id: string) => id !== currentDeviceId.value)
            } else {
              // 当设置为无货时，向 deviceId 数组中添加当前设备ID
              if (!deviceIds.includes(currentDeviceId.value)) {
                deviceIds.push(currentDeviceId.value)
              }
              item.deviceId = deviceIds
            }
          },
          configurable: true
        })
      }
    })
  }
  return res
}



// 多选事件
const selectionChangHandle = (objs: { id: string }[]) => {
  selectObjs.value = objs.map(({ id }) => id);
  multiple.value = !objs.length;
};



// 添加计算属性来判断商品状态
const getButtonProps = (row: any) => {
  const hasDevice = Array.isArray(row.deviceId) && row.deviceId.includes(currentDeviceId.value)
  return {
    type: hasDevice ? 'danger' : 'success',
    text: hasDevice ? '补货' : '下架'
  }
}

// 修改设备绑定状态变更函数
const handleDeviceBinding = async (row: any) => {
  if (!currentDeviceId.value) {
    useMessage().error('未获取到设备ID')
    return
  }

  // 判断当前状态
  const isCurrentlyInList = Array.isArray(row.deviceId) && row.deviceId.includes(currentDeviceId.value)

  try {
    const params = {
      code: row.code,
      deviceId: currentDeviceId.value,
      status: isCurrentlyInList ? 0 : 1,  // 当前在列表中时点击补货(0)，不在列表中时点击下架(1)
      type: 'shoes'
    }

    const res = await fetchUpdateDeviceId(params)
    if (res.code === 0) {
      useMessage().success(isCurrentlyInList ? '商品已补货' : '商品已下架')

      // 刷新数据以获取最新状态
      await getDataList(true)
    }
  } catch (err: any) {
    useMessage().error(err.msg || '操作失败')
  }
}

// 添加图片预览相关的响应式变量
const previewVisible = ref(false)
const previewImage = ref('')

// 添加图片预览处理函数
const handlePreviewImage = (imageUrl: string) => {
  previewImage.value = imageUrl
  previewVisible.value = true
}

// 查询操作
const handleQuery = async () => {
  getDataList(true);
}

// 重置操作
const handleReset = () => {
  if (queryRef.value) {
    queryRef.value.resetFields();
  }
  state.queryForm.name = '';
  state.queryForm.code = '';
  getDataList(true);
}

// 添加用户切换处理函数
const handleUserChange = () => {
  // 根据选择的用户名找到对应的设备ID
  const selectedUser = userDeviceList.value.find(item => item.username === currentUsername.value)
  if (selectedUser) {
    currentDeviceId.value = selectedUser.deviceId
  }
  getDataList(true);
}

// 添加设备状态相关函数
const getDeviceStatusType = (row: any, deviceId: string) => {
  // 使用新的stockStatus字段
  if (row.stockStatus && Array.isArray(row.stockStatus)) {
    const status = row.stockStatus.find((item: any) => item.deviceId === deviceId)
    return status?.stockStatus === '无货' ? 'danger' : 'success'
  }
  // 兼容旧的deviceId字段
  const hasDevice = Array.isArray(row.deviceId) && row.deviceId.includes(deviceId)
  return hasDevice ? 'danger' : 'success'
}

const getDeviceStatusText = (row: any, deviceId: string) => {
  // 使用新的stockStatus字段
  if (row.stockStatus && Array.isArray(row.stockStatus)) {
    const status = row.stockStatus.find((item: any) => item.deviceId === deviceId)
    return status?.stockStatus || '有货'
  }
  // 兼容旧的deviceId字段
  const hasDevice = Array.isArray(row.deviceId) && row.deviceId.includes(deviceId)
  return hasDevice ? '无货' : '有货'
}

// 获取设备对应的用户名
const getDeviceUsername = (row: any, deviceId: string) => {
  if (row.stockStatus && Array.isArray(row.stockStatus)) {
    const status = row.stockStatus.find((item: any) => item.deviceId === deviceId)
    return status?.username || deviceId
  }
  return deviceId
}

// 获取设备显示名称（用户名）
const getDeviceDisplayName = (row: any, deviceId: string) => {
  return getDeviceUsername(row, deviceId)
}

// 缩短设备显示名称
const getShortDeviceName = (row: any, deviceId: string) => {
  const displayName = getDeviceUsername(row, deviceId)
  if (displayName.length > 8) {
    return displayName.substring(0, 6) + '...'
  }
  return displayName
}



// 单个商品的全部下架功能
const handleBatchOutOfStockForProduct = async (row: any) => {
  if (userDeviceIds.value.length === 0) {
    useMessage().error('未获取到设备ID')
    return
  }

  try {
    await useMessageBox().confirm(`确定要将商品 "${row.name}" 在所有设备上下架吗？`)
  } catch {
    return
  }

  let successCount = 0
  let errorCount = 0

  for (const deviceId of userDeviceIds.value) {
    try {
      const res = await fetchUpdateDeviceId({
        code: row.code,
        deviceId: deviceId,
        status: 1, // 下架
        type: 'shoes'
      })

      if (res.code === 0) {
        successCount++
      } else {
        errorCount++
      }
    } catch (error) {
      errorCount++
    }
  }

  if (errorCount === 0) {
    useMessage().success(`成功在 ${successCount} 个设备上下架商品`)
  } else {
    useMessage().error(`操作完成：成功 ${successCount} 个，失败 ${errorCount} 个`)
  }

  // 刷新数据以获取最新状态
  await getDataList(true)
}

// 单个商品的全部补货功能
const handleBatchRestockForProduct = async (row: any) => {
  if (userDeviceIds.value.length === 0) {
    useMessage().error('未获取到设备ID')
    return
  }

  try {
    await useMessageBox().confirm(`确定要将商品 "${row.name}" 在所有设备上补货吗？`)
  } catch {
    return
  }

  let successCount = 0
  let errorCount = 0

  for (const deviceId of userDeviceIds.value) {
    try {
      const res = await fetchUpdateDeviceId({
        code: row.code,
        deviceId: deviceId,
        status: 0, // 补货
        type: 'shoes'
      })

      if (res.code === 0) {
        successCount++
      } else {
        errorCount++
      }
    } catch (error) {
      errorCount++
    }
  }

  if (errorCount === 0) {
    useMessage().success(`成功在 ${successCount} 个设备上补货商品`)
  } else {
    useMessage().error(`操作完成：成功 ${successCount} 个，失败 ${errorCount} 个`)
  }

  // 刷新数据以获取最新状态
  await getDataList(true)
}
</script>

<style lang="scss" scoped>
.image-slot {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  background: #f5f7fa;
  color: #909399;
  font-size: 20px;
}

:deep(.el-switch) {
  margin: 0 auto;
  display: block;
}

:deep(.preview-dialog) {
  .el-dialog__body {
    padding: 10px;
  }
}

.preview-container {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  min-height: 400px;
}

.device-status {
  display: flex;
  flex-wrap: wrap;
  gap: 2px;
  max-height: 60px;
  overflow: hidden;
}

.device-tag {
  font-size: 10px;
  margin: 1px 0;
  padding: 2px 4px;
  height: auto;
  line-height: 1.2;
}

.device-status-popover {
  max-height: 200px;
  overflow-y: auto;
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}
</style>