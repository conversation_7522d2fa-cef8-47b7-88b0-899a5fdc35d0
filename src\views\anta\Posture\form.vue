<template>
  <el-drawer :title="form.measureId ? (detail ? '详情' : '编辑') : '添加'" v-model="visible" size="50%">
      <el-form ref="dataFormRef" :model="form" :rules="dataRules" label-width="100px" :disabled="detail" v-loading="loading">
        <el-row :gutter="24">
    <el-col :span="12" class="mb20">
      <el-form-item label="姓名" prop="name">
        <el-input v-model="form.name" />
      </el-form-item>
      </el-col>
    <el-col :span="12" class="mb20">
      <el-form-item label="年龄" prop="age">
        <el-input v-model="form.age" />
      </el-form-item>
      </el-col>
    <el-col :span="12" class="mb20">
      <el-form-item label="性别" prop="gender">
        <el-input v-model="form.gender" />
      </el-form-item>
      </el-col>
    <el-col :span="12" class="mb20">
      <el-form-item label="创建时间" prop="createTime">
        <el-input v-model="form.createTime" />
      </el-form-item>
      </el-col>
    <el-col :span="12" class="mb20">
      <el-form-item label="肩角(°)" prop="shoulderAngle">
        <el-input v-model="form.shoulderAngle" />
      </el-form-item>
    </el-col>
    <el-col :span="12" class="mb20">
      <el-form-item label="左腿长" prop="leftLegLength">
        <el-input v-model="form.leftLegLength" />
      </el-form-item>
      </el-col>
    <el-col :span="12" class="mb20">
      <el-form-item label="右腿长" prop="rightLegLength">
        <el-input v-model="form.rightLegLength" />
      </el-form-item>
      </el-col>
    <el-col :span="12" class="mb20">
      <el-form-item label="左膝角" prop="leftKneeAngle">
        <el-input v-model="form.leftKneeAngle" />
      </el-form-item>
      </el-col>
    <el-col :span="12" class="mb20">
      <el-form-item label="右膝角" prop="rightKneeAngle">
        <el-input v-model="form.rightKneeAngle" />
      </el-form-item>
      </el-col>
    <el-col :span="12" class="mb20">
      <el-form-item label="踝间距" prop="ankleDistance">
        <el-input v-model="form.ankleDistance" />
      </el-form-item>
      </el-col>
    <el-col :span="12" class="mb20">
      <el-form-item label="头前伸距离" prop="headForward">
        <el-input v-model="form.headForward"/>
      </el-form-item>
      </el-col>
    <el-col :span="12" class="mb20">
      <el-form-item label="肩臀角" prop="shoulderHipAngle">
        <el-input v-model="form.shoulderHipAngle" />
      </el-form-item>
      </el-col>
    <el-col :span="12" class="mb20">
      <el-form-item label="左步幅" prop="leftStrides">
        <el-input v-model="form.leftStrides" />
      </el-form-item>
      </el-col>
    <el-col :span="12" class="mb20">
      <el-form-item label="右步幅" prop="rightStrides">
        <el-input v-model="form.rightStrides" />
      </el-form-item>
      </el-col>
    <el-col :span="12" class="mb20">
      <el-form-item label="步速" prop="stepSpeed">
        <el-input v-model="form.stepSpeed" />
      </el-form-item>
      </el-col>
    <el-col :span="12" class="mb20">
      <el-form-item label="左步长 cm" prop="leftStepLength">
        <el-input v-model="form.leftStepLength" />
      </el-form-item>
      </el-col>
    <el-col :span="12" class="mb20">
      <el-form-item label="右步长cm" prop="rightStepLength">
        <el-input v-model="form.rightStepLength" />
      </el-form-item>
      </el-col>
    <el-col :span="12" class="mb20">
      <el-form-item label="膝间距" prop="kneesDistance">
        <el-input v-model="form.kneesDistance" />
      </el-form-item>
      </el-col>
    <el-col :span="12" class="mb20">
      <el-form-item label="左步宽" prop="leftStepWidth">
        <el-input v-model="form.leftStepWidth"/>
      </el-form-item>
      </el-col>
    <el-col :span="12" class="mb20">
      <el-form-item label="右步宽" prop="rightStepWidth">
        <el-input v-model="form.rightStepWidth" />
      </el-form-item>
      </el-col>
    <el-col :span="12" class="mb20">
      <el-form-item label="步频" prop="stepFrequency">
        <el-input v-model="form.stepFrequency"/>
      </el-form-item>
      </el-col>
    <el-col :span="12" class="mb20">
      <el-form-item label="侧膝角" prop="kneeAngleSide">
        <el-input v-model="form.kneeAngleSide" />
      </el-form-item>
      </el-col>
    <el-col :span="12" class="mb20">
      <el-form-item label="左足角" prop="leftFootAngle">
        <el-input v-model="form.leftFootAngle" />
      </el-form-item>
      </el-col>
    <el-col :span="12" class="mb20">
      <el-form-item label="右足角" prop="rightFootAngle">
        <el-input v-model="form.rightFootAngle"/>
      </el-form-item>
      </el-col>
    <el-col :span="12" class="mb20">
      <el-form-item label="眼间距" prop="eyesDistance">
        <el-input v-model="form.eyesDistance" />
      </el-form-item>
      </el-col>
    <el-col :span="12" class="mb20">
      <el-form-item label="左眼宽" prop="leftEyeWidth">
        <el-input v-model="form.leftEyeWidth"/>
      </el-form-item>
      </el-col>
    <el-col :span="12" class="mb20">
      <el-form-item label="右眼宽" prop="rightEyeWidth">
        <el-input v-model="form.rightEyeWidth" />
      </el-form-item>
      </el-col>
    <el-col :span="12" class="mb20">
      <el-form-item label="头宽" prop="headWidth">
        <el-input v-model="form.headWidth" />
      </el-form-item>
      </el-col>
    <el-col :span="12" class="mb20">
      <el-form-item label="左臂长" prop="leftArmLenght">
        <el-input v-model="form.leftArmLenght"/>
      </el-form-item>
      </el-col>
    <el-col :span="12" class="mb20">
      <el-form-item label="右臂长" prop="rightArmLength">
        <el-input v-model="form.rightArmLength" />
      </el-form-item>
      </el-col>
    <el-col :span="12" class="mb20">
      <el-form-item label="躯干长" prop="bodyLength">
        <el-input v-model="form.bodyLength" />
      </el-form-item>
      </el-col>
    <el-col :span="12" class="mb20">
      <el-form-item label="肩宽" prop="shoulderWidth">
        <el-input v-model="form.shoulderWidth"/>
      </el-form-item>
      </el-col>
    </el-row>
  <el-row :gutter="24">
   
  </el-row>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="visible = false">取消</el-button>
          <el-button type="primary" @click="onSubmit" :disabled="loading">确认</el-button>
        </span>
      </template>
    </el-drawer>
</template>

<script setup lang="ts" name="PostureDialog">
import { useDict } from '/@/hooks/dict';
import { rule } from '/@/utils/validate';
import { useMessage } from "/@/hooks/message";
import { getObj, addObj, putObj, delChildObj } from '/@/api/anta/Posture'
const scFormTable = defineAsyncComponent(() => import('/@/components/FormTable/index.vue'));
const emit = defineEmits(['refresh']);

// 定义变量内容
const dataFormRef = ref();
const visible = ref(false);
const loading = ref(false);
const detail = ref(false);

// 定义字典

// 提交表单数据
const form = reactive({
    name: '',
    age: '',
    gender: '',
    createTime:'',
		measureId:'',
	  shoulderAngle: '',
	  shoulderLow: '',
	  shoulderHigh: '',
	  leftLegLength: '',
	  rightLegLength: '',
	  leftKneeAngle: '',
	  rightKneeAngle: '',
	  ankleDistance: '',
	  headForward: '',
	  shoulderHipAngle: '',
	  backTop: '',
	  video: '',
	  boneData: '',
	  measureType: '',
	  sideTop: '',
	  frontData: '',
	  sideData: '',
	  motionData: '',
	  deviceCode: '',
	  leftStrides: '',
	  rightStrides: '',
	  stepSpeed: '',
	  leftStepLength: '',
	  rightStepLength: '',
	  kneesDistance: '',
	  leftStepWidth: '',
	  rightStepWidth: '',
	  stepFrequency: '',
	  kneeAngleSide: '',
	  leftFootAngle: '',
	  rightFootAngle: '',
	  eyesDistance: '',
	  leftEyeWidth: '',
	  rightEyeWidth: '',
	  headWidth: '',
	  leftArmLenght: '',
	  rightArmLength: '',
	  bodyLength: '',
	  shoulderWidth: '',
	  atMeasureList:[],
});

const childTemp = reactive({
    id: '',
    userId: '',
    name: '',
    childrenId: '',
    deviceId: '',
    deviceCode: '',
    age: '',
    gender: '',
    motion: '',
    front: '',
    side: '',
    back: '',
    foot: '',
    bodyFat: '',
    reportQrcode: '',
    createTime: '',
    updateTime: '',
    delFlag: '',
    tenantId: '',
})

// 定义校验规则
const dataRules = ref({
        shoulderAngle: [{required: true, message: '肩角不能为空', trigger: 'blur'}],
        shoulderLow: [{required: true, message: '头中点偏向低肩点距离不能为空', trigger: 'blur'}],
        shoulderHigh: [{required: true, message: '头中点偏向高肩点距离不能为空', trigger: 'blur'}],
        leftLegLength: [{required: true, message: '左腿长不能为空', trigger: 'blur'}],
        rightLegLength: [{required: true, message: '右腿长不能为空', trigger: 'blur'}],
        leftKneeAngle: [{required: true, message: '左膝角不能为空', trigger: 'blur'}],
        rightKneeAngle: [{required: true, message: '右膝角不能为空', trigger: 'blur'}],
        ankleDistance: [{required: true, message: '踝间距不能为空', trigger: 'blur'}],
        headForward: [{required: true, message: '头前伸距离不能为空', trigger: 'blur'}],
        shoulderHipAngle: [{required: true, message: '肩臀角不能为空', trigger: 'blur'}],
        backTop: [{required: true, message: '背面上半身不能为空', trigger: 'blur'}],
        video: [{required: true, message: '视频不能为空', trigger: 'blur'}],
        boneData: [{required: true, message: '骨骼数据不能为空', trigger: 'blur'}],
        measureType: [{required: true, message: '${field.fieldComment}不能为空', trigger: 'blur'}],
        sideTop: [{required: true, message: '侧面上半身不能为空', trigger: 'blur'}],
        frontData: [{required: true, message: '${field.fieldComment}不能为空', trigger: 'blur'}],
        sideData: [{required: true, message: '${field.fieldComment}不能为空', trigger: 'blur'}],
        motionData: [{required: true, message: '${field.fieldComment}不能为空', trigger: 'blur'}],
        deviceCode: [{required: true, message: '设备编码不能为空', trigger: 'blur'}],
        leftStrides: [{required: true, message: '左步幅不能为空', trigger: 'blur'}],
        rightStrides: [{required: true, message: '右步幅不能为空', trigger: 'blur'}],
        stepSpeed: [{required: true, message: '步速（米/分）不能为空', trigger: 'blur'}],
        leftStepLength: [{required: true, message: '左步长 cm不能为空', trigger: 'blur'}],
        rightStepLength: [{required: true, message: '右步长cm不能为空', trigger: 'blur'}],
        kneesDistance: [{required: true, message: '膝间距不能为空', trigger: 'blur'}],
        leftStepWidth: [{required: true, message: '左步宽不能为空', trigger: 'blur'}],
        rightStepWidth: [{required: true, message: '右步宽不能为空', trigger: 'blur'}],
        stepFrequency: [{required: true, message: '步频（步/分）不能为空', trigger: 'blur'}],
        kneeAngleSide: [{required: true, message: '侧膝角不能为空', trigger: 'blur'}],
        leftFootAngle: [{required: true, message: '左足角不能为空', trigger: 'blur'}],
        rightFootAngle: [{required: true, message: '右足角不能为空', trigger: 'blur'}],
        eyesDistance: [{required: true, message: '眼间距不能为空', trigger: 'blur'}],
        leftEyeWidth: [{required: true, message: '左眼宽不能为空', trigger: 'blur'}],
        rightEyeWidth: [{required: true, message: '右眼宽不能为空', trigger: 'blur'}],
        headWidth: [{required: true, message: '头宽不能为空', trigger: 'blur'}],
        leftArmLenght: [{required: true, message: '左臂长不能为空', trigger: 'blur'}],
        rightArmLength: [{required: true, message: '右臂长不能为空', trigger: 'blur'}],
        bodyLength: [{required: true, message: '躯干长不能为空', trigger: 'blur'}],
        shoulderWidth: [{required: true, message: '肩宽不能为空', trigger: 'blur'}],
})

// 打开弹窗
const openDialog = (id: string, isDetail: boolean) => {
  visible.value = true
  detail.value = isDetail
  form.measureId = ''

  // 重置表单数据
  nextTick(() => {
    dataFormRef.value?.resetFields();
    form.atMeasureList = [];
  });

  // 获取posture信息
  if (id) {
    form.measureId = id
    getPostureData(id)
  }
};

// 提交
const onSubmit = async () => {
  const valid = await dataFormRef.value.validate().catch(() => {});
  if (!valid) return false;

  try {
    loading.value = true;
    form.measureId ? await putObj(form) : await addObj(form);
    useMessage().success(form.measureId ? '修改成功' : '添加成功');
    visible.value = false;
    emit('refresh');
  } catch (err: any) {
    useMessage().error(err.msg);
  } finally {
    loading.value = false;
  }
};
// 删除子表数据
const deleteChild = async (obj: { id: string }) => {
  if (obj.id) {
    try {
      await delChildObj([obj.id]);
      useMessage().success('删除成功');
    } catch (err: any) {
      useMessage().error(err.msg);
    }
  }
};

// 初始化表单数据
const getPostureData = (id: string) => {
  // 获取数据
  getObj(id).then((res: any) => {
    Object.assign(form, res.data)
  })
};

// 暴露变量
defineExpose({
  openDialog
});
</script>