<template>
  <div class="layout-padding">
    <div class="layout-padding-auto layout-padding-view">
      <!-- 添加搜索表单 -->
      <el-form :model="state.queryForm" ref="queryRef" :inline="true" class="mb15">
        <el-form-item label="商品编码" prop="code">
          <el-input v-model="state.queryForm.code" placeholder="请输入商品编码" clearable style="width: 200px"
            @keyup.enter="handleQuery" />
        </el-form-item>
        <el-form-item label="商品名称" prop="name">
          <el-input v-model="state.queryForm.name" placeholder="请输入商品名称" clearable style="width: 200px"
            @keyup.enter="handleQuery" />
        </el-form-item>
        <el-form-item label="特征" prop="character">
          <el-select v-model="state.queryForm.character" placeholder="请选择特征" clearable style="width: 200px">
            <el-option label="大童常规楦" value="dtRegular"></el-option>
            <el-option label="大童宽楦" value="dtWidth"></el-option>
            <el-option label="大童窄楦" value="dtNarrow"></el-option>
            <el-option label="小童宽楦" value="xtWidth"></el-option>
            <el-option label="小童常规楦" value="xtRegular"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="性别" prop="gender">
          <el-select v-model="state.queryForm.gender" placeholder="请选择性别" clearable style="width: 200px">
            <el-option label="男" :value=0></el-option>
            <el-option label="女" :value=1></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="门店设备编码" prop="deviceId">
          <el-select v-model="state.queryForm.deviceId" placeholder="请选择门店设备编码" clearable style="width: 200px">
            <el-option v-for="deviceId in deviceIdOptions" :key="deviceId" :label="deviceId" :value="deviceId" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
          <el-button icon="Refresh" @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>
      <!-- <el-row>
        <div class="mb8" style="width: 100%">
          <el-button icon="folder-add" type="primary" class="ml10" @click="formDialogRef.openDialog()">
            新 增
          </el-button>
          <el-button plain :disabled="multiple" icon="Delete" type="primary" @click="handleDelete(selectObjs)">
            删除
          </el-button>
          <right-toolbar v-model:showSearch="showSearch" :export="'anta_recommend_export'" @exportExcel="exportExcel"
            class="ml10 mr20" style="float: right;" @queryTable="getDataList"></right-toolbar>
        </div>
      </el-row> -->
      <el-row>
        <div class="mb8" style="width: 100%">
          <el-button :icon="Plus" type="primary" class="ml10" @click="handleAdd">
            新 增
          </el-button>
          <el-button plain :disabled="!selectObjs.length" :icon="Delete" type="danger"
            @click="handleDelete(selectObjs)">
            删除
          </el-button>
          <el-button icon="Upload" type="primary" class="ml10" @click="importDialogVisible = true">
            导入
          </el-button>
          <el-button icon="Download" type="primary" class="ml10" @click="handleExport" :loading="exportLoading">
            导出
          </el-button>
        </div>
      </el-row>
      <el-table :data="state.dataList" v-loading="state.loading" border :cell-style="tableStyle.cellStyle"
        :header-cell-style="tableStyle.headerCellStyle" @selection-change="selectionChangHandle"
        @sort-change="sortChangeHandle">
        <el-table-column type="selection" width="40" align="center" />
        <el-table-column type="index" label="id" width="50" />
        <el-table-column prop="code" label="商品编码" show-overflow-tooltip />
        <el-table-column prop="name" label="商品名称" show-overflow-tooltip />
        <el-table-column prop="image" label="商品图片" width="120">
          <template #default="scope">
            <el-image :src="scope.row.image" fit="contain" style="width: 80px; height: 80px; cursor: pointer;"
              @click="handlePreviewImage(scope.row.image)">
              <template #error>
                <div class="image-slot">
                  <el-icon>
                    <Picture />
                  </el-icon>
                </div>
              </template>
            </el-image>
          </template>
        </el-table-column>
        <el-table-column prop="character" label="特征" show-overflow-tooltip>
          <template #default="scope">
            <span>{{ formatCharacter(scope.row.character) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="gender" label="性别" show-overflow-tooltip>
          <template #default="scope">
            <span>{{ scope.row.gender === 0 || scope.row.gender === '0' ? '男' : scope.row.gender === 1 ||
              scope.row.gender === '1' ? '女' : '' }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="amount" label="价格" show-overflow-tooltip />


        <el-table-column label="操作" width="180">
          <template #default="scope">
            <el-button type="primary" size="small" @click="handleEdit(scope.row)">
              编辑
            </el-button>
            <el-button type="danger" size="small" @click="handleDeleteSingle(scope.row)">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <pagination @size-change="sizeChangeHandle" @current-change="currentChangeHandle" v-bind="state.pagination" />
    </div>

    <!-- 编辑对话框 -->
    <el-dialog v-model="editDialogVisible" title="编辑商品" width="500px">
      <el-form :model="editForm" ref="editFormRef" label-width="100px">
        <el-form-item label="商品编码" prop="code">
          <el-input v-model="editForm.code" placeholder="请输入商品编码" :disabled="true" />
        </el-form-item>
        <el-form-item label="商品名称" prop="name">
          <el-input v-model="editForm.name" placeholder="请输入商品名称" />
        </el-form-item>
        <el-form-item label="价格" prop="amount">
          <el-input v-model="editForm.amount" placeholder="请输入价格" />
        </el-form-item>
        <el-form-item label="特征" prop="character">
          <el-select v-model="editForm.character" placeholder="请选择特征">
            <el-option label="大童常规楦" value="dtRegular"></el-option>
            <el-option label="大童宽楦" value="dtWidth"></el-option>
            <el-option label="大童窄楦" value="dtNarrow"></el-option>
            <el-option label="小童宽楦" value="xtWidth"></el-option>
            <el-option label="小童常规楦" value="xtRegular"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="性别" prop="gender">
          <el-select v-model="editForm.gender" placeholder="请选择性别">
            <el-option label="男" :value="0"></el-option>
            <el-option label="女" :value="1"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="商品图片" prop="image">
          <upload-img v-model:image-url="editForm.image" :drag="true" :file-size="200" />
        </el-form-item>
        <el-form-item label="内容列表" prop="content">
          <div v-for="(item, index) in editForm.content || []" :key="index" class="content-item">
            <el-input v-model="item.content" placeholder="请输入内容描述"
              style="width: calc(100% - 80px); margin-right: 10px;" />
            <el-button type="danger" @click="removeEditContent(index)"
              :disabled="(editForm.content || []).length <= 1">删除</el-button>
          </div>
          <div class="content-actions">
            <el-button type="primary" @click="addEditContent"
              :disabled="(editForm.content || []).length >= 5">添加内容</el-button>
            <span class="content-tip" v-if="(editForm.content || []).length >= 5">最多添加5个内容项</span>
          </div>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="editDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleUpdate">确认</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 添加图片预览对话框 -->
    <el-dialog v-model="previewVisible" width="800px" :modal="true" :append-to-body="true" :close-on-click-modal="true"
      :show-close="true" class="preview-dialog" :z-index="3000">
      <div class="preview-container">
        <img :src="previewImage" style="max-width: 100%; max-height: 80vh; object-fit: contain;">
      </div>
    </el-dialog>

    <!-- 添加新增对话框 -->
    <el-dialog v-model="addDialogVisible" title="新增商品" width="500px" @open="resetAddForm" @close="closeAddForm">
      <el-form :model="addForm" ref="addFormRef" label-width="100px" :rules="addFormRules">
        <el-form-item label="商品编码" prop="code">
          <el-input v-model="addForm.code" placeholder="请输入商品编码" />
        </el-form-item>
        <el-form-item label="商品名称" prop="name">
          <el-input v-model="addForm.name" placeholder="请输入商品名称" />
        </el-form-item>
        <el-form-item label="价格" prop="amount">
          <el-input v-model="addForm.amount" placeholder="请输入价格" />
        </el-form-item>
        <el-form-item label="特征" prop="character">
          <el-select v-model="addForm.character" placeholder="请选择特征">
            <el-option label="大童常规楦" value="dtRegular"></el-option>
            <el-option label="大童宽楦" value="dtWidth"></el-option>
            <el-option label="大童窄楦" value="dtNarrow"></el-option>
            <el-option label="小童宽楦" value="xtWidth"></el-option>
            <el-option label="小童常规楦" value="xtRegular"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="性别" prop="gender">
          <el-select v-model="addForm.gender" placeholder="请选择性别">
            <el-option label="男" :value="0"></el-option>
            <el-option label="女" :value="1"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="商品图片" prop="image">
          <upload-img v-model:image-url="addForm.image" :drag="true" :file-size="200" />
        </el-form-item>
        <el-form-item label="内容列表" prop="content">
          <div v-for="(item, index) in addForm.content || []" :key="index" class="content-item">
            <el-input v-model="item.content" placeholder="请输入内容描述"
              style="width: calc(100% - 80px); margin-right: 10px;" />
            <el-button type="danger" @click="removeAddContent(index)"
              :disabled="(addForm.content || []).length <= 1">删除</el-button>
          </div>
          <div class="content-actions">
            <el-button type="primary" @click="addAddContent"
              :disabled="(addForm.content || []).length >= 5">添加内容</el-button>
            <span class="content-tip" v-if="(addForm.content || []).length >= 5">最多添加5个内容项</span>
          </div>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="addDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitAddForm">确认</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 导入弹窗 -->
    <el-dialog v-model="importDialogVisible" title="导入商品" width="400px">
      <el-upload drag :before-upload="beforeImportUpload" :custom-request="customRequest" :show-file-list="false"
        accept=".xlsx,.xls,.csv">
        <el-icon class="el-icon--upload"><upload-filled /></el-icon>
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
      </el-upload>
      <div style="margin-top: 16px; text-align: right;">
        <el-button type="success" @click="handleDownloadTemplate">下载导入模板</el-button>
      </div>
      <template #footer>
        <el-button @click="importDialogVisible = false">关闭</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts" name="systemRecommend">
import { Picture, Plus, Delete } from '@element-plus/icons-vue'
import { BasicTableProps, useTable } from "/@/hooks/table";
import { fetchUpdateProduct, fetchAddProduct, fetchDeleteProduct, fetchBatchDeleteProducts, fetchExportProduct, fetchImportProduct } from "/@/api/anta/recommend";
import { fetchListshoes, fetchShoeTypeSearch } from "/@/api/anta/ShoeType";
import { getAllDeviceIds } from "/@/api/admin/user";

import { useMessage, useMessageBox } from "/@/hooks/message";
import { useUserInfo } from '/@/stores/userInfo';
import { ref, reactive, nextTick, onMounted } from 'vue';

// 定义变量内容
const queryRef = ref()
// 多选变量
const selectObjs = ref([]) as any
const multiple = ref(true)

const state: BasicTableProps = reactive<BasicTableProps>({
  queryForm: {
    name: '',
    code: '',
    character: '',
    gender: '',
    sceneType: '',
    distanceLevel: '',
    deviceId: ''
  },
  pageList: fetchListshoes
})

// 添加设备ID选项的响应式变量
const deviceIdOptions = ref<string[]>([])

//  table hook
const {
  getDataList: originalGetDataList,
  currentChangeHandle,
  sizeChangeHandle,
  sortChangeHandle,
  tableStyle
} = useTable(state)

// 重写 getDataList 方法
const getDataList = async (isSearch?: boolean) => {
  const res = await originalGetDataList(isSearch)

  // 为每个商品添加 _inStock 属性
  if (state.dataList) {
    state.dataList.forEach((item: any) => {
      // 使用 defineProperty 来添加响应式属性
      if (!item.hasOwnProperty('_inStock')) {
        Object.defineProperty(item, '_inStock', {
          get: () => {
            // 如果 deviceId 不存在或不是数组，说明没有无货记录，返回 true（有货）
            if (!item.deviceId || !Array.isArray(item.deviceId)) {
              return true
            }
            // 如果 deviceId 数组中包含当前设备ID，说明是无货，返回 false
            // 否则说明是有货，返回 true
            return !item.deviceId.includes(currentDeviceId.value)
          },
          set: (value: boolean) => {
            const deviceIds = Array.isArray(item.deviceId) ? [...item.deviceId] : []
            if (value) {
              // 当设置为有货时，从 deviceId 数组中移除当前设备ID
              item.deviceId = deviceIds.filter((id: string) => id !== currentDeviceId.value)
            } else {
              // 当设置为无货时，向 deviceId 数组中添加当前设备ID
              if (!deviceIds.includes(currentDeviceId.value)) {
                deviceIds.push(currentDeviceId.value)
              }
              item.deviceId = deviceIds
            }
          },
          configurable: true
        })
      }
    })
  }
  return res
}

// 多选事件
const selectionChangHandle = (objs: any[]) => {
  selectObjs.value = objs;
  multiple.value = !objs.length;
};

// 添加设备ID相关的状态
const currentDeviceId = ref('')

// 在组件挂载时获取设备ID
onMounted(() => {
  const data = useUserInfo().userInfos;
  currentDeviceId.value = data.user.deviceId
  // 获取所有设备ID列表
  getDeviceIdList()
})

// 获取设备ID列表的函数
const getDeviceIdList = async () => {
  try {
    const res = await getAllDeviceIds()
    if (res.code === 0) {
      deviceIdOptions.value = res.data || []
    } else {
      useMessage().error('获取设备ID列表失败')
    }
  } catch (error: any) {
    useMessage().error('获取设备ID列表失败')
  }
}

// 添加图片预览相关的响应式变量
const previewVisible = ref(false)
const previewImage = ref('')

// 添加图片预览处理函数
const handlePreviewImage = (imageUrl: string) => {
  previewImage.value = imageUrl
  previewVisible.value = true
}

// 查询操作
const handleQuery = async () => {
  // 修改判断逻辑，特别处理gender为0的情况
  const hasGender = state.queryForm.gender === 0 || state.queryForm.gender === 1 || state.queryForm.gender;

  if (state.queryForm.name || state.queryForm.code || state.queryForm.character ||
    hasGender || state.queryForm.sceneType || state.queryForm.distanceLevel || state.queryForm.deviceId) {
    state.loading = true;
    try {
      const res = await fetchShoeTypeSearch({
        code: state.queryForm.code,
        name: state.queryForm.name,
        character: state.queryForm.character,
        gender: state.queryForm.gender,
        sceneType: state.queryForm.sceneType,
        distanceLevel: state.queryForm.distanceLevel,
        deviceId: state.queryForm.deviceId
      });
      if (res.code === 0) {
        state.dataList = res.data.records || [];
        if (state.pagination) {
          state.pagination.total = res.data.total || 0;
        }
      }
    } catch (error) {
      useMessage().error('查询失败');
    } finally {
      state.loading = false;
    }
  } else {
    getDataList(true);
  }
}

// 重置操作
const handleReset = () => {
  if (queryRef.value) {
    queryRef.value.resetFields();
  }
  state.queryForm.name = '';
  state.queryForm.code = '';
  state.queryForm.character = '';
  state.queryForm.gender = '';
  state.queryForm.sceneType = '';
  state.queryForm.distanceLevel = '';
  state.queryForm.deviceId = '';
  getDataList(true);
}

// 编辑相关的响应式变量
const editDialogVisible = ref(false)
const editForm = ref({
  id: '',
  name: '',
  image: '',
  code: '',
  amount: '',
  character: '',
  gender: '',
  content: [] as { content: string }[]
})
const editFormRef = ref()

// 新增相关的响应式变量
const addDialogVisible = ref(false)
const addForm = ref({
  name: '',
  image: '',
  code: '',
  amount: '',
  character: '',
  gender: '',
  content: [] as { content: string }[]
})
const addFormRef = ref()

// 添加表单验证规则
const addFormRules = {
  code: [
    { required: true, message: '请输入商品编码', trigger: 'blur' }
  ],
  name: [
    { required: true, message: '请输入商品名称', trigger: 'blur' }
  ],
  amount: [
    { required: true, message: '请输入价格', trigger: 'blur' }
  ],
  image: [
    { required: true, message: '请上传商品图片', trigger: 'change' }
  ],
  character: [
    { required: true, message: '请选择特征', trigger: 'change' }
  ],
  gender: [
    { required: true, message: '请选择性别', trigger: 'change' }
  ]
}

// 打开新增表单时重置表单
const resetAddForm = () => {
  // 重置表单数据
  addForm.value = {
    name: '',
    image: '',
    code: '',
    amount: '',
    character: '',
    gender: '',
    content: [{ content: '' }] // 初始化时添加一个空内容项
  }
  // 延迟一帧重置验证状态，避免还没创建好表单就重置
  nextTick(() => {
    if (addFormRef.value) {
      addFormRef.value.resetFields()
    }
  })
}

// 关闭新增表单时的处理
const closeAddForm = () => {
  // 重置表单验证状态
  if (addFormRef.value) {
    addFormRef.value.resetFields()
  }
}

// 格式化特征字段显示
const formatCharacter = (character: string) => {
  if (!character) return '';

  switch (character) {
    case 'bigBmi': return '大体重';
    case 'normal': return '正常';
    case 'lowArch': return '低足弓';
    case 'highArch': return '高足弓';
    case 'dtRegular': return '大童常规楦';
    case 'dtWidth': return '大童宽楦';
    case 'dtNarrow': return '大童窄楦';
    case 'xtWidth': return '小童宽楦';
    case 'xtRegular': return '小童常规楦';
    default: return character;
  }
}

// 添加编辑表单的内容项
const addEditContent = () => {
  editForm.value.content.push({ content: '' });
}

// 删除编辑表单的内容项
const removeEditContent = (index: number) => {
  if (editForm.value.content.length > 1) {
    editForm.value.content.splice(index, 1);
  } else {
    useMessage().wraning('至少保留一项内容');
  }
}

// 添加新增表单的内容项
const addAddContent = () => {
  if (!addForm.value.content) {
    addForm.value.content = [];
  }
  if (addForm.value.content.length < 5) {
    addForm.value.content.push({ content: '' });
  }
}

// 删除新增表单的内容项
const removeAddContent = (index: number) => {
  if (addForm.value.content.length > 1) {
    addForm.value.content.splice(index, 1);
  } else {
    useMessage().wraning('至少保留一项内容');
  }
}

// 新增表单提交
const submitAddForm = async () => {
  if (!addFormRef.value) return

  const valid = await addFormRef.value.validate().catch(() => false)
  if (!valid) return

  try {
    const params = {
      code: addForm.value.code,
      name: addForm.value.name,
      image: addForm.value.image,
      amount: addForm.value.amount,
      type: 'shoes',
      character: addForm.value.character,
      gender: addForm.value.gender,
      content: addForm.value.content || [] // 直接传递数组，不需要转换为字符串
    }
    const res = await fetchAddProduct(params)
    if (res.code === 0) {
      useMessage().success('添加成功')
      addDialogVisible.value = false
      await getDataList(true)
    } else {
      useMessage().error(res.msg || '添加失败')
    }
  } catch (error: any) {
    useMessage().error(error.msg || '添加失败')
  }
}

// 替换原有的新增按钮点击事件
const handleAdd = () => {
  addDialogVisible.value = true
}

// 删除商品（批量）
const handleDelete = async (rows: any) => {
  if (!rows || rows.length === 0) {
    useMessage().error('请选择要删除的商品')
    return
  }

  try {
    await useMessageBox().confirm('确定要删除选中的商品吗？')

    // 获取所有选中商品的code
    const codes = rows.map((row: any) => row.code).filter((code: string) => code)

    if (codes.length === 0) {
      useMessage().error('无法获取商品编码')
      return
    }

    // 调用批量删除API
    const res = await fetchBatchDeleteProducts(codes, 'shoes')

    if (res.code === 0) {
      useMessage().success('删除成功')
      await getDataList(true)
    } else {
      useMessage().error(res.msg || '删除失败')
    }
  } catch (error: any) {
    if (error !== 'cancel') {
      useMessage().error(error.msg || '删除失败')
    }
  }
}

// 编辑按钮点击事件
const handleEdit = (row: any) => {
  // 解析content字段，如果是字符串则尝试解析为JSON对象
  let contentArray = [];
  if (row.content) {
    try {
      // 检查content是否为字符串类型
      if (typeof row.content === 'string') {
        contentArray = JSON.parse(row.content);
      } else {
        // 如果已经是对象，直接使用
        contentArray = row.content;
      }
    } catch (error) {
      // 解析content字段失败，使用空数组
      contentArray = [];
    }
  }

  // 确保至少有一个内容项
  if (contentArray.length === 0) {
    contentArray.push({ content: '' });
  }

  editForm.value = {
    id: row.id,
    name: row.name,
    image: row.image,
    code: row.code,
    amount: row.amount,
    character: row.character,
    gender: row.gender,
    content: contentArray
  }
  editDialogVisible.value = true
}

// 更新商品信息
const handleUpdate = async () => {
  try {
    const params = {
      code: editForm.value.code,
      name: editForm.value.name,
      image: editForm.value.image,
      amount: editForm.value.amount,
      type: 'shoes',
      character: editForm.value.character,
      gender: editForm.value.gender,
      content: editForm.value.content || [], // 直接传递数组，不需要转换为字符串
      operationType: 'UPDATE_INFO' // 明确指定为产品信息更新操作
    }
    const res = await fetchUpdateProduct(params)
    if (res.code === 0) {
      useMessage().success('更新成功')
      editDialogVisible.value = false
      // 传入 false 参数，保持当前页码
      await getDataList(false)
    }
  } catch (error: any) {
    useMessage().error(error.msg || '更新失败')
  }
}

// 处理单个商品的删除
const handleDeleteSingle = async (row: any) => {
  if (!row || !row.code) {
    useMessage().error('无法获取商品编码')
    return
  }

  try {
    await useMessageBox().confirm('确定要删除该商品吗？')

    const params = {
      code: row.code,
      type: 'shoes'
    }

    const res = await fetchDeleteProduct(params)
    if (res.code === 0) {
      useMessage().success('删除成功')
      await getDataList(true)
    } else {
      useMessage().error(res.msg || '删除失败')
    }
  } catch (error: any) {
    if (error !== 'cancel') {
      useMessage().error(error.msg || '删除失败')
    }
  }
}

// 导入导出相关
const importDialogVisible = ref(false)

const beforeImportUpload = (file: File) => {
  const name = file.name.toLowerCase();
  const isExcel = name.endsWith('.xlsx') || name.endsWith('.xls') || name.endsWith('.csv');
  if (!isExcel) {
    useMessage().error('只能上传 Excel、CSV 文件');
    return false;
  }
  customRequest({ file });
  return false;
};

const customRequest = async (option: any) => {
  try {
    await customImport(option.file);
    option.onSuccess({}, option.file); // 必须调用，通知el-upload上传成功
  } catch (e) {
    option.onError(e);
  }
};

const customImport = async (file: File) => {
  const formData = new FormData();
  formData.append('file', file);

  // 检查文件类型
  const isExcel = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    || file.type === 'application/vnd.ms-excel'
    || file.name.endsWith('.csv');

  if (!isExcel) {
    useMessage().error('只能上传 Excel 或 CSV 文件');
    return;
  }

  try {
    const res: any = await fetchImportProduct(formData);
    if (res.code === 0) {
      // 判断导入结果
      const { successCount, failCount, failList } = res.data || {};
      if (successCount > 0) {
        useMessage().success(`导入成功：${successCount} 条，失败：${failCount || 0} 条`);
        if (failCount > 0 && Array.isArray(failList) && failList.length > 0) {
          // 失败详情弹窗
          let msg = failList.map(item => `第${item.row}行：${item.reason}${item.code ? '（编码：' + item.code + '）' : ''}`).join('\n');
          useMessage().error(`部分数据导入失败：\n${msg}`);
        }
        importDialogVisible.value = false;
        getDataList(true);
      } else {
        // 全部失败
        let msg = '导入失败';
        if (failCount > 0 && Array.isArray(failList) && failList.length > 0) {
          msg = failList.map(item => `第${item.row}行：${item.reason}${item.code ? '（编码：' + item.code + '）' : ''}`).join('\n');
        } else if (res.msg) {
          msg = res.msg;
        }
        useMessage().error(msg);
      }
    } else {
      useMessage().error(res.msg || '导入失败');
    }
  } catch (e: any) {
    useMessage().error(e?.msg || '导入失败');
  }
};

const exportLoading = ref(false)

const handleExport = async () => {
  exportLoading.value = true;
  try {
    const res: any = await fetchExportProduct(state.queryForm);
    if (!res) throw new Error('导出失败');
    const blob = new Blob([res], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.setAttribute('download', '商品列表.xlsx');
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);
    useMessage().success('导出成功');
  } catch (e: any) {
    useMessage().error(e?.msg || '导出失败');
  } finally {
    exportLoading.value = false;
  }
}

const handleDownloadTemplate = async () => {
  try {
    const url = 'https://gai-clouds.obs.cn-east-3.myhuaweicloud.com/%E5%95%86%E5%93%81%E5%88%97%E8%A1%A8.xlsx';
    const link = document.createElement('a');
    link.href = url;
    link.setAttribute('download', '商品导入模板.xlsx');
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    useMessage().success('模板下载成功');
  } catch (e: any) {
    useMessage().error(e?.msg || '模板下载失败');
  }
}
</script>

<style lang="scss" scoped>
.image-slot {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  background: #f5f7fa;
  color: #909399;
  font-size: 20px;
}

:deep(.el-switch) {
  margin: 0 auto;
  display: block;
}

:deep(.preview-dialog) {
  .el-dialog__body {
    padding: 10px;
  }
}

.preview-container {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  min-height: 400px;
}

.avatar-uploader {
  :deep(.el-upload) {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    transition: var(--el-transition-duration-fast);

    &:hover {
      border-color: var(--el-color-primary);
    }
  }
}

.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 178px;
  height: 178px;
  text-align: center;
  line-height: 178px;
}

.avatar {
  width: 178px;
  height: 178px;
  display: block;
}

/* 内容列表样式 */
.content-item {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.content-actions {
  display: flex;
  align-items: center;
  margin-top: 10px;
}

.content-tip {
  margin-left: 10px;
  color: #909399;
  font-size: 12px;
}
</style>