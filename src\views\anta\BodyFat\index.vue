<template>
  <div class="layout-padding">
    <div class="layout-padding-auto layout-padding-view">
      <el-row class="ml10" v-show="showSearch">
        <el-form :inline="true" :model="state.queryForm" @keyup.enter="getDataList" ref="queryRef">
          <el-form-item>
            <el-input placeholder="姓名" style="max-width: 180px" v-model.trim="state.queryForm.name" />
          </el-form-item>
          <el-form-item>
            <el-input placeholder="门店" style="max-width: 180px" v-model.trim="state.queryForm.storeName" />
          </el-form-item>
          <el-form-item>
            <el-date-picker v-model="state.queryForm.dateRange" type="daterange" range-separator="至"
              start-placeholder="开始日期" end-placeholder="结束日期" style="max-width: 300px" value-format="YYYY-MM-DD" />
          </el-form-item>
          <el-form-item>
            <el-button @click="handleQuery" icon="search" type="primary">
              {{ $t('common.queryBtn') }}
            </el-button>
            <el-button @click="resetQuery" icon="Refresh">{{ $t('common.resetBtn') }}</el-button>
          </el-form-item>
        </el-form>
      </el-row>
      <el-row>
        <div class="mb8" style="width: 100%">
          <el-button @click="formDialogRef.openDialog()" class="ml10" icon="folder-add" type="primary">
            {{ $t('common.addBtn') }}
          </el-button>
          <!-- <el-button plain @click="excelUploadRef.show()" class="ml10" icon="upload-filled" type="primary">
            {{ $t('common.importBtn') }}
          </el-button> -->
          <el-button plain :disabled="multiple" @click="handleDelete(selectObjs)" class="ml10" icon="Delete"
            type="primary">
            {{ $t('common.delBtn') }}
          </el-button>
          <right-toolbar :export="'sys_post_export'" @exportExcel="exportExcel" @queryTable="getDataList" class="ml10"
            style="float: right; margin-right: 20px" v-model:showSearch="showSearch"></right-toolbar>
        </div>
      </el-row>

      <el-table :data="state.dataList" @selection-change="selectionChangeHandle" v-loading="state.loading" border
        :cell-style="tableStyle.cellStyle" :header-cell-style="tableStyle.headerCellStyle"
        @sort-change="sortChangeHandle">
        <el-table-column type="selection" width="40" align="center" />
        <el-table-column type="index" label="#" width="45" />
        <el-table-column prop="deviceCode" label="设备编号" show-overflow-tooltip width="100" />
        <el-table-column prop="storeName" label="门店" show-overflow-tooltip width="100" />
        <el-table-column prop="name" label="名字" show-overflow-tooltip />
        <el-table-column prop="age" label="年龄" show-overflow-tooltip />
        <el-table-column prop="gender" label="性别" show-overflow-tooltip />
        <el-table-column prop="createTime" label="创建时间" show-overflow-tooltip width="100" />
        <el-table-column prop="height" label="身高(cm)" show-overflow-tooltip width="100" />
        <el-table-column prop="weight" label="体重(kg)" show-overflow-tooltip width="100" />
        <el-table-column prop="bmi" label="bmi" show-overflow-tooltip width="100" />
        <el-table-column prop="bfr" label="体脂率(%)" show-overflow-tooltip width="100" />
        <el-table-column prop="bwr" label="水分率(%)" show-overflow-tooltip width="100" />
        <el-table-column prop="bmr" label="基础代谢率(%)" show-overflow-tooltip width="120" />
        <el-table-column prop="bod" label="肥胖度(%)" show-overflow-tooltip width="100" />
        <el-table-column prop="bmc" label="骨含量(%)" show-overflow-tooltip width="100" />
        <el-table-column prop="slm" label="肌肉含量(%)" show-overflow-tooltip width="110" />
        <!-- <el-table-column :label="$t('common.action')" width="200">
          <template #default="scope">
            <el-button text type="primary" icon="view" v-auth="'sys_role_edit'"
              @click="formDialogRef.openDialog(scope.row.id, true)">
              详情
            </el-button>
            <el-button icon="edit-pen" @click="formDialogRef.openDialog(scope.row.postId)" text type="primary" v-auth="'sys_post_edit'"
							>{{ $t('common.editBtn') }}
						</el-button>
						<el-button icon="delete" @click="handleDelete([scope.row.postId])" text type="primary" v-auth="'sys_post_del'"
							>{{ $t('common.delBtn') }}
						</el-button>
          </template>
</el-table-column> -->
      </el-table>
      <pagination @current-change="currentChangeHandle" @size-change="sizeChangeHandle" v-bind="state.pagination" />
    </div>

    <!-- 编辑、新增  -->
    <form-dialog ref="formDialogRef" @refresh="getDataList(false)" />
    <upload-excel :title="$t('post.importPostTip')" @refreshDataList="getDataList" ref="excelUploadRef"
      temp-url="/admin/sys-file/local/file/post.xlsx" url="/admin/post/import" />
  </div>
  <!-- 导入excel -->

</template>

<script setup lang="ts" name="systemBodyFat">
import { BasicTableProps, useTable } from "/@/hooks/table";
import { fetchList, delObjs } from "/@/api/anta/BodyFat";
import { useMessage, useMessageBox } from "/@/hooks/message";
// import { useDict } from '/@/hooks/dict';
// import { useI18n } from 'vue-i18n';
// 引入组件
const FormDialog = defineAsyncComponent(() => import('./form.vue'));
// const { t } = useI18n();
// 定义查询字典

// 定义变量内容
const formDialogRef = ref();
const excelUploadRef = ref();
// 搜索变量
const queryRef = ref()
const showSearch = ref(true)
// 多选变量
const selectObjs = ref([]) as any
const multiple = ref(true)

const initialQueryForm = {
  name: '',
  deviceCode: '',
  dateRange: [],
  storeName: ''
}
const state: BasicTableProps = reactive<BasicTableProps>({
  queryForm: { ...initialQueryForm },
  pageList: fetchList
})

//  table hook
const {
  getDataList,
  currentChangeHandle,
  sizeChangeHandle,
  sortChangeHandle,
  downBlobFile,
  tableStyle
} = useTable(state)

// 查询参数处理
const handleQuery = () => {
  let startTime = ''
  let endTime = ''
  if (
    Array.isArray(state.queryForm.dateRange) &&
    state.queryForm.dateRange.length === 2 &&
    /^\d{4}-\d{2}-\d{2}$/.test(state.queryForm.dateRange[0]) &&
    /^\d{4}-\d{2}-\d{2}$/.test(state.queryForm.dateRange[1])
  ) {
    startTime = state.queryForm.dateRange[0]
    endTime = state.queryForm.dateRange[1]
  }
  const params = {
    name: state.queryForm.name,
    storeName: state.queryForm.storeName,
    deviceCode: state.queryForm.deviceCode,
    startTime,
    endTime
  }
  fetchList(params).then(res => {
    state.dataList = res.data.records
    state.pagination && (state.pagination.total = res.data.total)
  })
}

// 清空搜索条件
const resetQuery = () => {
  Object.assign(state.queryForm, initialQueryForm)
  state.queryForm.startTime = ''
  state.queryForm.endTime = ''
  state.queryForm.storeName = ''
  selectObjs.value = []
  queryRef.value && queryRef.value.resetFields && queryRef.value.resetFields()
  getDataList()
}

const exportExcel = () => {
  // 拆分时间范围
  let startTime = ''
  let endTime = ''
  if (
    Array.isArray(state.queryForm.dateRange) &&
    state.queryForm.dateRange.length === 2 &&
    /^\d{4}-\d{2}-\d{2}$/.test(state.queryForm.dateRange[0]) &&
    /^\d{4}-\d{2}-\d{2}$/.test(state.queryForm.dateRange[1])
  ) {
    startTime = state.queryForm.dateRange[0]
    endTime = state.queryForm.dateRange[1]
  }
  const params = {
    name: state.queryForm.name,
    deviceCode: state.queryForm.deviceCode,
    startTime,
    endTime
  }
  downBlobFile('/anta/footShapeData/export', params, '体脂数据.xlsx')
}

// 多选事件
const selectionChangeHandle = (objs: { measureId: string }[]) => {
  selectObjs.value = objs.map(({ measureId }) => measureId);
  multiple.value = !objs.length;
};

// 删除操作
const handleDelete = async (ids: string[]) => {
  try {
    await useMessageBox().confirm('此操作将永久删除');
  } catch {
    return;
  }

  try {
    await delObjs(ids);
    getDataList();
    useMessage().success('删除成功');
  } catch (err: any) {
    useMessage().error(err.msg);
  }
};
</script>