export default {
	refund: {
		index: '#',
		importpayRefundOrderTip: '导入退款订单表',
		refundOrderId: '退款订单号',
		payOrderId: '支付订单号',
		channelPayOrderNo: '渠道支付单号',
		mchId: ' 商户ID',
		mchRefundNo: '商户退款单号',
		channelId: '渠道ID',
		payAmount: '支付金额',
		refundAmount: '退款金额',
		currency: ' 三位货币代码',
		status: '退款状态',
		result: '退款结果',
		clientIp: '客户端IP',
		device: '设备',
		remark: '备注',
		channelUser: ' 渠道用户标识',
		username: ' 用户姓名',
		channelMchId: ' 渠道商户ID',
		channelOrderNo: ' 渠道订单号',
		channelErrCode: ' 渠道错误码',
		channelErrMsg: ' 渠道错误描述',
		extra: ' 特定渠道发起时额外参数',
		notifyUrl: ' 通知地址',
		param1: ' 扩展参数1',
		param2: ' 扩展参数2',
		expireTime: '订单失效时间',
		refundSuccTime: '订单退款成功时间',
		delFlag: ' delFlag',
		createTime: '创建时间',
		updateTime: '更新时间',
		tenantId: '租户ID',
		inputRefundOrderIdTip: '请输入退款订单号',
		inputPayOrderIdTip: '请输入支付订单号',
		inputChannelPayOrderNoTip: '请输入渠道支付单号',
		inputMchIdTip: '请输入商户ID',
		inputMchRefundNoTip: '请输入商户退款单号',
		inputChannelIdTip: '请输入渠道ID',
		inputPayAmountTip: '请输入支付金额',
		inputRefundAmountTip: '请输入退款金额',
		inputCurrencyTip: '请输入三位货币代码',
		inputStatusTip: '请输入退款状态',
		inputResultTip: '请输入退款结果',
		inputClientIpTip: '请输入客户端IP',
		inputDeviceTip: '请输入设备',
		inputRemarkTip: '请输入备注',
		inputChannelUserTip: '请输入渠道用户标识',
		inputUsernameTip: '请输入用户姓名',
		inputChannelMchIdTip: '请输入渠道商户ID',
		inputChannelOrderNoTip: '请输入渠道订单号',
		inputChannelErrCodeTip: '请输入渠道错误码',
		inputChannelErrMsgTip: '请输入渠道错误描述',
		inputExtraTip: '请输入特定渠道发起时额外参数',
		inputNotifyUrlTip: '请输入通知地址',
		inputParam1Tip: '请输入扩展参数1',
		inputParam2Tip: '请输入扩展参数2',
		inputExpireTimeTip: '请输入订单失效时间',
		inputRefundSuccTimeTip: '请输入订单退款成功时间',
		inputDelFlagTip: '请输入 delFlag',
		inputCreateTimeTip: '请输入创建时间',
		inputUpdateTimeTip: '请输入更新时间',
		inputTenantIdTip: '请输入租户ID',
	},
};
