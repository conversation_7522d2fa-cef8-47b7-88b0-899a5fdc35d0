<template>
  <el-drawer :title="form.id ? (detail ? '详情' : '编辑') : '添加'" v-model="visible" size="50%">
      <el-form ref="dataFormRef" :model="form" label-width="100px" :disabled="detail" v-loading="loading">
        <el-row :gutter="24">
    <el-col :span="12" class="mb20">
      <el-form-item label="姓名" prop="name">
        <el-input v-model="form.name" />
      </el-form-item>
      </el-col>
    <el-col :span="12" class="mb20">
      <el-form-item label="年龄" prop="age">
        <el-input v-model="form.age" />
      </el-form-item>
      </el-col>
    <el-col :span="12" class="mb20">
      <el-form-item label="性别" prop="gender">
        <el-input v-model="form.gender" />
      </el-form-item>
      </el-col>
    <el-col :span="12" class="mb20">
      <el-form-item label="创建时间" prop="createTime">
        <el-input v-model="form.createTime" />
      </el-form-item>
      </el-col>
      <el-col :span="12" class="mb20">
      <el-form-item label="左足型" prop="leftFootType">
        <el-input v-model="form.leftFootType" />
      </el-form-item>
      </el-col>
    <el-col :span="12" class="mb20">
      <el-form-item label="右足型" prop="rightFootType">
        <el-input v-model="form.rightFootType" />
      </el-form-item>
      </el-col>
      <el-col :span="12" class="mb20">
      <el-form-item label="左足长" prop="leftFootLength">
        <el-input v-model="form.leftFootLength" />
      </el-form-item>
      </el-col>
    <el-col :span="12" class="mb20">
      <el-form-item label="右足长" prop="rightFootLength">
        <el-input v-model="form.rightFootLength" />
      </el-form-item>
      </el-col>
    <el-col :span="12" class="mb20">
      <el-form-item label="左足宽" prop="leftFootWidth">
        <el-input v-model="form.leftFootWidth"/>
      </el-form-item>
      </el-col>
    <el-col :span="12" class="mb20">
      <el-form-item label="右足宽" prop="rightFootWidth">
        <el-input v-model="form.rightFootWidth" />
      </el-form-item>
      </el-col>
    <el-col :span="12" class="mb20">
      <el-form-item label="左足弓系数" prop="leftFootarch">
        <el-input v-model="form.leftFootarch" />
      </el-form-item>
      </el-col>
    <el-col :span="12" class="mb20">
      <el-form-item label="右足弓系数" prop="rightFootarch">
        <el-input v-model="form.rightFootarch" />
      </el-form-item>
      </el-col>
      <el-col :span="12" class="mb20">
      <el-form-item label="左足弓长" prop="leftFootarchLength">
        <el-input v-model="form.leftFootarchLength"/>
      </el-form-item>
      </el-col>
    <el-col :span="12" class="mb20">
      <el-form-item label="右足弓长" prop="rightFootarchLength">
        <el-input v-model="form.rightFootarchLength"/>
      </el-form-item>
      </el-col>
    <el-col :span="12" class="mb20">
      <el-form-item label="左足弓高" prop="leftFootarchHeight">
        <el-input v-model="form.leftFootarchHeight"/>
      </el-form-item>
      </el-col>
    <el-col :span="12" class="mb20">
      <el-form-item label="右足弓高" prop="rightFootarchHeight">
        <el-input v-model="form.rightFootarchHeight" />
      </el-form-item>
      </el-col>
    </el-row>
    <el-col :span="12" class="mb20">
      <el-form-item label="左足后跟宽" prop="leftRearFootWidth">
        <el-input v-model="form.leftRearFootWidth" />
      </el-form-item>
      </el-col>
    <el-col :span="12" class="mb20">
      <el-form-item label="右足后跟宽" prop="rightRearFootWidth">
        <el-input v-model="form.rightRearFootWidth"/>
      </el-form-item>
      </el-col>
   
    <el-col :span="12" class="mb20">
      <el-form-item label="左拇指外翻" prop="leftFirstToeAngle">
        <el-input v-model="form.leftFirstToeAngle" />
      </el-form-item>
      </el-col>
    <el-col :span="12" class="mb20">
      <el-form-item label="右拇指外翻" prop="rightFirstToeAngle">
        <el-input v-model="form.rightFirstToeAngle" />
      </el-form-item>
      </el-col>
    <el-col :span="12" class="mb20">
      <el-form-item label="左小指外翻" prop="leftLittleToeAngle">
        <el-input v-model="form.leftLittleToeAngle" />
      </el-form-item>
      </el-col>
    <el-col :span="12" class="mb20">
      <el-form-item label="右小指外翻" prop="rightLittleToeAngle">
        <el-input v-model="form.rightLittleToeAngle" />
      </el-form-item>
      </el-col>
   
    <el-col :span="12" class="mb20">
      <el-form-item label=" 左拇指高" prop="leftFirstToeHeight">
        <el-input v-model="form.leftFirstToeHeight"/>
      </el-form-item>
      </el-col>
    <el-col :span="12" class="mb20">
      <el-form-item label=" 右拇指高" prop="rightFirstToeHeight">
        <el-input v-model="form.rightFirstToeHeight" />
      </el-form-item>
      </el-col>
    <el-col :span="12" class="mb20">
      <el-form-item label=" 左脚背高" prop="leftFootHeight">
        <el-input v-model="form.leftFootHeight" />
      </el-form-item>
      </el-col>
    <el-col :span="12" class="mb20">
      <el-form-item label=" 右脚背高" prop="rightFootHeight">
        <el-input v-model="form.rightFootHeight" />
      </el-form-item>
      </el-col>

  <el-row :gutter="24">
   
  </el-row>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="visible = false">取消</el-button>
          <el-button type="primary" @click="onSubmit" :disabled="loading">确认</el-button>
        </span>
      </template>
    </el-drawer>
</template>

<script setup lang="ts" name="FootShapeDataDialog">
// import { useDict } from '/@/hooks/dict';
// import { rule } from '/@/utils/validate';
import { useMessage } from "/@/hooks/message";
import { getObj, addObj, putObj, delChildObj } from '/@/api/anta/FootShapeData'
// const scFormTable = defineAsyncComponent(() => import('/@/components/FormTable/index.vue'));
const emit = defineEmits(['refresh']);

// 定义变量内容
const dataFormRef = ref();
const visible = ref(false);
const loading = ref(false);
const detail = ref(false);

// 定义字典

// 提交表单数据
const form = reactive({
		id:'',
	  name: '',
	  age: '',
	  gender: '',
	  createTime: '',
	  leftFootarch: '',
	  rightFootarch: '',
	  leftFootLength: '',
	  rightFootLength: '',
	  leftFootWidth: '',
	  rightFootWidth: '',
	  leftLengthWidthRate: '',
	  rightLengthWidthRate: '',
	  leftFootPrint: '',
	  rightFootPrint: '',
	  leftFootPrintOrigin: '',
	  rightFootPrintOrigin: '',
	  leftToeWidth: '',
	  rightToeWidth: '',
	  leftRearFootWidth: '',
	  rightRearFootWidth: '',
	  leftFirstToeAngle: '',
	  rightFirstToeAngle: '',
	  leftLittleToeAngle: '',
	  rightLittleToeAngle: '',
	  leftFootType: '',
	  rightFootType: '',
	  leftArchHeight: '',
	  rightArchHeight: '',
	  leftFirstToeHeight: '',
	  rightFirstToeHeight: '',
	  leftFootHeight: '',
	  rightFootHeight: '',
	  leftFootarchLength: '',
	  rightFootarchLength: '',
	  leftFootarchHeight: '',
	  rightFootarchHeight: '',
	  atMeasureList:[],
});

// const childTemp = reactive({
//     id: '',
//     userId: '',
//     name: '',
//     childrenId: '',
//     deviceId: '',
//     deviceCode: '',
//     age: '',
//     gender: '',
//     motion: '',
//     front: '',
//     side: '',
//     back: '',
//     foot: '',
//     bodyFat: '',
//     reportQrcode: '',
//     createTime: '',
//     updateTime: '',
//     delFlag: '',
//     tenantId: '',
// })

// 定义校验规则
// const dataRules = ref({
//         measureId: [{required: true, message: '测量Id不能为空', trigger: 'blur'}],
//         deviceCode: [{required: true, message: '设备Id不能为空', trigger: 'blur'}],
//         height: [{required: true, message: '身高不能为空', trigger: 'blur'}],
//         weight: [{required: true, message: '体重不能为空', trigger: 'blur'}],
//         leftFootarch: [{required: true, message: '左足弓不能为空', trigger: 'blur'}],
//         rightFootarch: [{required: true, message: '右足弓不能为空', trigger: 'blur'}],
//         leftFootLength: [{required: true, message: '左足长不能为空', trigger: 'blur'}],
//         rightFootLength: [{required: true, message: '右足长不能为空', trigger: 'blur'}],
//         leftFootWidth: [{required: true, message: '左足宽不能为空', trigger: 'blur'}],
//         rightFootWidth: [{required: true, message: '右足宽不能为空', trigger: 'blur'}],
//         leftLengthWidthRate: [{required: true, message: '左足长宽比不能为空', trigger: 'blur'}],
//         rightLengthWidthRate: [{required: true, message: '${field.fieldComment}不能为空', trigger: 'blur'}],
//         leftFootPrint: [{required: true, message: '左足印图不能为空', trigger: 'blur'}],
//         rightFootPrint: [{required: true, message: '${field.fieldComment}不能为空', trigger: 'blur'}],
//         leftFootPrintOrigin: [{required: true, message: '左足印原始图不能为空', trigger: 'blur'}],
//         rightFootPrintOrigin: [{required: true, message: '右足印原始图不能为空', trigger: 'blur'}],
//         leftToeWidth: [{required: true, message: '左趾宽不能为空', trigger: 'blur'}],
//         rightToeWidth: [{required: true, message: '右趾宽不能为空', trigger: 'blur'}],
//         leftRearFootWidth: [{required: true, message: '左足后跟宽不能为空', trigger: 'blur'}],
//         rightRearFootWidth: [{required: true, message: '右足后跟宽不能为空', trigger: 'blur'}],
//         leftFirstToeAngle: [{required: true, message: '左大脚趾不能为空', trigger: 'blur'}],
//         rightFirstToeAngle: [{required: true, message: '右大脚趾不能为空', trigger: 'blur'}],
//         leftLittleToeAngle: [{required: true, message: '左小脚趾不能为空', trigger: 'blur'}],
//         rightLittleToeAngle: [{required: true, message: '右小脚趾不能为空', trigger: 'blur'}],
//         leftFootType: [{required: true, message: '左足型不能为空', trigger: 'blur'}],
//         rightFootType: [{required: true, message: '右足型不能为空', trigger: 'blur'}],
//         leftArchHeight: [{required: true, message: '${field.fieldComment}不能为空', trigger: 'blur'}],
//         rightArchHeight: [{required: true, message: '${field.fieldComment}不能为空', trigger: 'blur'}],
//         leftFirstToeHeight: [{required: true, message: '${field.fieldComment}不能为空', trigger: 'blur'}],
//         rightFirstToeHeight: [{required: true, message: '${field.fieldComment}不能为空', trigger: 'blur'}],
//         leftFootHeight: [{required: true, message: '${field.fieldComment}不能为空', trigger: 'blur'}],
//         rightFootHeight: [{required: true, message: '${field.fieldComment}不能为空', trigger: 'blur'}],
//         leftFootarchLength: [{required: true, message: '左足弓长不能为空', trigger: 'blur'}],
//         rightFootarchLength: [{required: true, message: '右足弓长不能为空', trigger: 'blur'}],
//         leftFootarchHeight: [{required: true, message: '左足弓高不能为空', trigger: 'blur'}],
//         rightFootarchHeight: [{required: true, message: '右足弓高不能为空', trigger: 'blur'}],
// })

// 打开弹窗
const openDialog = (id: string, isDetail: boolean) => {
  visible.value = true
  detail.value = isDetail
  form.id = ''

  // 重置表单数据
  nextTick(() => {
    dataFormRef.value?.resetFields();
    form.atMeasureList = [];
  });

  // 获取footShapeData信息
  if (id) {
    form.id = id
    getFootShapeDataData(id)
  }
};

// 提交
const onSubmit = async () => {
  const valid = await dataFormRef.value.validate().catch(() => {});
  if (!valid) return false;

  try {
    loading.value = true;
    form.id ? await putObj(form) : await addObj(form);
    useMessage().success(form.id ? '修改成功' : '添加成功');
    visible.value = false;
    emit('refresh');
  } catch (err: any) {
    useMessage().error(err.msg);
  } finally {
    loading.value = false;
  }
};
// 删除子表数据
const deleteChild = async (obj: { id: string }) => {
  if (obj.id) {
    try {
      await delChildObj([obj.id]);
      useMessage().success('删除成功');
    } catch (err: any) {
      useMessage().error(err.msg);
    }
  }
};

// 初始化表单数据
const getFootShapeDataData = (id: string) => {
  // 获取数据
  getObj(id).then((res: any) => {
    Object.assign(form, res.data)
  })
};

// 暴露变量
defineExpose({
  openDialog
});
</script>