<template>
  <div class="layout-padding">
    <div class="layout-padding-auto layout-padding-view">
      <!-- 添加搜索表单 -->
      <el-form :model="state.queryForm" ref="queryRef" :inline="true" class="mb15">
        <el-form-item label="商品名称" prop="name">
          <el-input v-model="state.queryForm.name" placeholder="请输入商品名称" clearable style="width: 200px"
            @keyup.enter="handleQuery" />
        </el-form-item>
        <el-form-item label="场景类型" prop="sceneType">
          <el-select v-model="state.queryForm.sceneType" placeholder="请选择场景类型" clearable style="width: 200px">
            <el-option v-for="item in sceneTypeOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="跑者类型" prop="distanceLevel">
          <el-select v-model="state.queryForm.distanceLevel" placeholder="请选择跑者类型" clearable style="width: 200px">
            <el-option v-for="item in distanceLevelOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
          <el-button icon="Refresh" @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>
      <!-- <el-row>
        <div class="mb8" style="width: 100%">
          <el-button icon="folder-add" type="primary" class="ml10" @click="formDialogRef.openDialog()">
            新 增
          </el-button>
          <el-button plain :disabled="multiple" icon="Delete" type="primary" @click="handleDelete(selectObjs)">
            删除
          </el-button>
          <right-toolbar v-model:showSearch="showSearch" :export="'anta_recommend_export'" @exportExcel="exportExcel"
            class="ml10 mr20" style="float: right;" @queryTable="getDataList"></right-toolbar>
        </div>
      </el-row> -->
      <el-table :data="state.dataList" v-loading="state.loading" border :cell-style="tableStyle.cellStyle"
        :header-cell-style="tableStyle.headerCellStyle" @selection-change="selectionChangHandle"
        @sort-change="sortChangeHandle">
        <el-table-column type="selection" width="40" align="center" />
        <el-table-column type="index" label="id" width="50" />
        <el-table-column prop="name" label="商品名称" show-overflow-tooltip />
        <el-table-column prop="image" label="商品图片" width="120">
          <template #default="scope">
            <el-image :src="scope.row.image" fit="contain" style="width: 80px; height: 80px; cursor: pointer;"
              @click="handlePreviewImage(scope.row.image)">
              <template #error>
                <div class="image-slot">
                  <el-icon>
                    <Picture />
                  </el-icon>
                </div>
              </template>
            </el-image>
          </template>
        </el-table-column>
        <el-table-column prop="sceneType" label="场景类型" show-overflow-tooltip />
        <el-table-column prop="distanceLevel" label="跑者类型" show-overflow-tooltip />

        <el-table-column label="操作" width="120">
          <template #default="scope">
            <el-button type="primary" size="small" @click="handleEdit(scope.row)">
              编辑
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <pagination @size-change="sizeChangeHandle" @current-change="currentChangeHandle" v-bind="state.pagination" />
    </div>

    <!-- 编辑对话框 -->
    <el-dialog v-model="editDialogVisible" title="编辑商品" width="500px">
      <el-form :model="editForm" ref="editFormRef" label-width="100px">
        <el-form-item label="商品名称" prop="name">
          <el-input v-model="editForm.name" placeholder="请输入商品名称" />
        </el-form-item>
        <el-form-item label="商品图片" prop="image">
          <upload-img v-model:image-url="editForm.image" :drag="true" :file-size="200" />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="editDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleUpdate">确认</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 添加图片预览对话框 -->
    <el-dialog v-model="previewVisible" width="800px" :modal="true" :append-to-body="true" :close-on-click-modal="true"
      :show-close="true" class="preview-dialog" :z-index="3000">
      <div class="preview-container">
        <img :src="previewImage" style="max-width: 100%; max-height: 80vh; object-fit: contain;">
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts" name="systemRecommend">
import { Picture, Plus } from '@element-plus/icons-vue'
import { BasicTableProps, useTable } from "/@/hooks/table";
import { fetchListProducts, fetchUpdateDeviceId, fetchSearch, fetchUpdateProduct } from "/@/api/anta/recommend";
import { fetchListshoes, fetchShoeTypeSearch } from "/@/api/anta/ShoeType";

import { useMessage, useMessageBox } from "/@/hooks/message";
import { useDict } from '/@/hooks/dict';
import { useUserInfo } from '/@/stores/userInfo';

// 定义变量内容
const formDialogRef = ref()

const detailDialogRef = ref()
// 搜索变量
const queryRef = ref()
const showSearch = ref(true)
// 多选变量
const selectObjs = ref([]) as any
const multiple = ref(true)

// 添加下拉选项数据
const sceneTypeOptions = ref([
  { value: '竞赛', label: '竞赛' },
  { value: '竞训', label: '竞训' },
  { value: '田径', label: '田径' },
  { value: '越野', label: '越野' },
  { value: '慢跑', label: '慢跑' },
  { value: '通勤', label: '通勤' }
])

const distanceLevelOptions = ref([
  { value: '<40km(入门跑者)', label: '<40km(入门跑者)' },
  { value: '40-100km(进阶跑者)', label: '40-100km(进阶跑者)' },
  { value: '>100km(精英跑者)', label: '>100km(精英跑者)' }
])

const state: BasicTableProps = reactive<BasicTableProps>({
  queryForm: {
    name: '',
    sceneType: '',
    distanceLevel: ''
  },
  pageList: fetchListshoes
})

//  table hook
const {
  getDataList: originalGetDataList,
  currentChangeHandle,
  sizeChangeHandle,
  sortChangeHandle,
  downBlobFile,
  tableStyle
} = useTable(state)

// 重写 getDataList 方法
const getDataList = async (isSearch?: boolean) => {
  const res = await originalGetDataList(isSearch)

  // 为每个商品添加 _inStock 属性
  if (state.dataList) {
    state.dataList.forEach((item: any) => {
      // 使用 defineProperty 来添加响应式属性
      if (!item.hasOwnProperty('_inStock')) {
        Object.defineProperty(item, '_inStock', {
          get: () => {
            // 如果 deviceId 不存在或不是数组，说明没有无货记录，返回 true（有货）
            if (!item.deviceId || !Array.isArray(item.deviceId)) {
              return true
            }
            // 如果 deviceId 数组中包含当前设备ID，说明是无货，返回 false
            // 否则说明是有货，返回 true
            return !item.deviceId.includes(currentDeviceId.value)
          },
          set: (value: boolean) => {
            const deviceIds = Array.isArray(item.deviceId) ? [...item.deviceId] : []
            if (value) {
              // 当设置为有货时，从 deviceId 数组中移除当前设备ID
              item.deviceId = deviceIds.filter((id: string) => id !== currentDeviceId.value)
            } else {
              // 当设置为无货时，向 deviceId 数组中添加当前设备ID
              if (!deviceIds.includes(currentDeviceId.value)) {
                deviceIds.push(currentDeviceId.value)
              }
              item.deviceId = deviceIds
            }
          },
          configurable: true
        })
      }
    })
  }
  return res
}

// 导出excel
const exportExcel = () => {
  downBlobFile('/anta/recommend/export', Object.assign(state.queryForm, { ids: selectObjs }), 'recommend.xlsx')
}

// 多选事件
const selectionChangHandle = (objs: { id: string }[]) => {
  selectObjs.value = objs.map(({ id }) => id);
  multiple.value = !objs.length;
};


// 添加设备ID相关的状态
const currentDeviceId = ref('')


// 在组件挂载时获取设备ID
onMounted(() => {
  const data = useUserInfo().userInfos;
  currentDeviceId.value = data.user.deviceId
  console.log("我是currentDeviceId.value的值", data.user.deviceId)
})

// 添加判断商品是否有货的函数
const isProductAvailable = (row: any) => {
  if (!row.deviceId || !currentDeviceId.value) return false
  // 确保 deviceId 是数组
  const deviceIds = Array.isArray(row.deviceId) ? row.deviceId : []
  return deviceIds.includes(currentDeviceId.value)
}

// 添加计算属性来判断商品状态
const getButtonProps = (row: any) => {
  const hasDevice = Array.isArray(row.deviceId) && row.deviceId.includes(currentDeviceId.value)
  return {
    type: hasDevice ? 'danger' : 'success',
    text: hasDevice ? '补货' : '下架'
  }
}

// 修改设备绑定状态变更函数
const handleDeviceBinding = async (row: any) => {
  if (!currentDeviceId.value) {
    useMessage().error('未获取到设备ID')
    return
  }

  // 判断当前状态
  const isCurrentlyInList = Array.isArray(row.deviceId) && row.deviceId.includes(currentDeviceId.value)

  try {
    const params = {
      code: row.code,
      deviceId: currentDeviceId.value,
      status: isCurrentlyInList ? 0 : 1,  // 当前在列表中时点击补货(0)，不在列表中时点击下架(1)
      type: 'shoes'
    }

    const res = await fetchUpdateDeviceId(params)
    if (res.code === 0) {
      useMessage().success(isCurrentlyInList ? '商品已补货' : '商品已下架')

      // 更新本地数据
      let newDeviceIds = Array.isArray(row.deviceId) ? [...row.deviceId] : []

      if (isCurrentlyInList) {
        // 补货：从数组中移除当前设备ID
        newDeviceIds = newDeviceIds.filter((id: string) => id !== currentDeviceId.value)
      } else {
        // 下架：添加当前设备ID到数组中
        if (!newDeviceIds.includes(currentDeviceId.value)) {
          newDeviceIds.push(currentDeviceId.value)
        }
      }

      // 直接更新行数据
      row.deviceId = [...newDeviceIds]
    }
  } catch (err: any) {
    useMessage().error(err.msg || '操作失败')
  }
}

// 添加图片预览相关的响应式变量
const previewVisible = ref(false)
const previewImage = ref('')

// 添加图片预览处理函数
const handlePreviewImage = (imageUrl: string) => {
  previewImage.value = imageUrl
  previewVisible.value = true
}

// 查询操作
const handleQuery = async () => {
  if (state.queryForm.name || state.queryForm.sceneType || state.queryForm.distanceLevel) {
    state.loading = true;
    try {
      const res = await fetchShoeTypeSearch({
        name: state.queryForm.name,
        sceneType: state.queryForm.sceneType,
        distanceLevel: state.queryForm.distanceLevel
      });
      if (res.code === 0) {
        state.dataList = res.data.records || [];
        if (state.pagination) {
          state.pagination.total = res.data.total || 0;
        }
      }
    } catch (error) {
      useMessage().error('查询失败');
    } finally {
      state.loading = false;
    }
  } else {
    getDataList(true);
  }
}

// 重置操作
const handleReset = () => {
  if (queryRef.value) {
    queryRef.value.resetFields();
  }
  state.queryForm.name = '';
  state.queryForm.sceneType = '';
  state.queryForm.distanceLevel = '';
  getDataList(true);
}

// 编辑相关的响应式变量
const editDialogVisible = ref(false)
const editForm = ref({
  id: '',
  name: '',
  image: '',
  code: ''
})
const editFormRef = ref()

// 编辑按钮点击事件
const handleEdit = (row: any) => {
  editForm.value = {
    id: row.id,
    name: row.name,
    image: row.image,
    code: row.code
  }
  editDialogVisible.value = true
}

// 图片上传前的验证
const beforeImageUpload = (file: File) => {
  const isImage = file.type.startsWith('image/')
  const isLt2M = file.size / 1024 / 1024 < 2

  if (!isImage) {
    useMessage().error('上传文件只能是图片格式!')
    return false
  }
  if (!isLt2M) {
    useMessage().error('上传图片大小不能超过 2MB!')
    return false
  }
  return true
}

// 处理图片上传
const handleImageUpload = async (params: any) => {
  const formData = new FormData()
  formData.append('file', params.file)
  try {
    // 这里需要替换成你的图片上传接口
    const response = await fetch('/your-upload-endpoint', {
      method: 'POST',
      body: formData
    })
    const result = await response.json()
    editForm.value.image = result.url
  } catch (error) {
    useMessage().error('图片上传失败')
  }
}

// 更新商品信息
const handleUpdate = async () => {
  try {
    const params = {
      code: editForm.value.code,
      name: editForm.value.name,
      image: editForm.value.image,
      operationType: 'UPDATE_INFO' // 明确指定为产品信息更新操作
    }
    const res = await fetchUpdateProduct(params)
    if (res.code === 0) {
      useMessage().success('更新成功')
      editDialogVisible.value = false
      // 传入 false 参数，保持当前页码
      await getDataList(false)
    }
  } catch (error: any) {
    useMessage().error(error.msg || '更新失败')
  }
}
</script>

<style lang="scss" scoped>
.image-slot {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  background: #f5f7fa;
  color: #909399;
  font-size: 20px;
}

:deep(.el-switch) {
  margin: 0 auto;
  display: block;
}

:deep(.preview-dialog) {
  .el-dialog__body {
    padding: 10px;
  }
}

.preview-container {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  min-height: 400px;
}

.avatar-uploader {
  :deep(.el-upload) {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    transition: var(--el-transition-duration-fast);

    &:hover {
      border-color: var(--el-color-primary);
    }
  }
}

.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 178px;
  height: 178px;
  text-align: center;
  line-height: 178px;
}

.avatar {
  width: 178px;
  height: 178px;
  display: block;
}
</style>