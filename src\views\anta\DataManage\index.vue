<template>
  <div class="layout-padding">
    <div class="layout-padding-auto layout-padding-view">
      <el-row class="ml10">
        <el-form :inline="true" :model="searchForm" class="mb15">
          <el-form-item label="手机号">
            <el-input v-model.trim="searchForm.phone" placeholder="请输入手机号" clearable style="width: 200px" />
          </el-form-item>
          <el-form-item label="门店">
            <el-input v-model.trim="searchForm.storeName" placeholder="请输入门店" clearable style="width: 200px" />
          </el-form-item>
          <el-form-item label="时间范围">
            <el-date-picker v-model="searchForm.dateRange" type="daterange" range-separator="至" start-placeholder="开始日期"
              end-placeholder="结束日期" value-format="YYYY-MM-DD" style="max-width: 300px" :shortcuts="dateShortcuts" />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="Search" @click="fetchData">查询</el-button>
            <el-button icon="Refresh" @click="resetForm">重置</el-button>
            <el-button type="success" icon="Download" @click="exportData">导出</el-button>
          </el-form-item>
        </el-form>
      </el-row>
      <el-table :data="tableData" border style="width: 100%; margin-bottom: 20px;">
        <el-table-column prop="deviceCode" label="设备编码" show-overflow-tooltip width="100" />
        <el-table-column prop="storeName" label="门店" show-overflow-tooltip width="100" />
        <el-table-column prop="name" label="姓名" show-overflow-tooltip />
        <el-table-column prop="phone" label="手机号" show-overflow-tooltip />
        <el-table-column prop="birthday" label="生日" show-overflow-tooltip />
        <el-table-column prop="gender" label="性别" show-overflow-tooltip width="60" />
        <el-table-column prop="leftFootLength" label="左足长" show-overflow-tooltip />
        <el-table-column prop="leftFootWidth" label="左足宽" show-overflow-tooltip />
        <el-table-column prop="leftFootarch" label="左足弓" show-overflow-tooltip />
        <el-table-column prop="leftFootarchHeight" label="左足弓高度" show-overflow-tooltip />
        <el-table-column prop="leftFootarchLength" label="左足弓长度" show-overflow-tooltip />
        <el-table-column prop="leftFirstToeAngle" label="左大脚趾角" show-overflow-tooltip />
        <el-table-column prop="leftLittleToeAngle" label="左小脚趾角" show-overflow-tooltip />
        <!-- <el-table-column prop="leftFirstToeHeight" label="左拇指高度" show-overflow-tooltip />
        <el-table-column prop="leftFootHeight" label="左足背高" show-overflow-tooltip /> -->
        <el-table-column prop="rightFootLength" label="右足长" show-overflow-tooltip />
        <el-table-column prop="rightFootWidth" label="右足宽" show-overflow-tooltip />
        <el-table-column prop="rightFootarch" label="右足弓" show-overflow-tooltip />
        <el-table-column prop="rightFootarchHeight" label="右足弓高度" show-overflow-tooltip />
        <el-table-column prop="rightFootarchLength" label="右足弓长度" show-overflow-tooltip />
        <el-table-column prop="rightFirstToeAngle" label="右大脚趾角" show-overflow-tooltip />
        <el-table-column prop="rightLittleToeAngle" label="右小脚趾角" show-overflow-tooltip />
        <!-- <el-table-column prop="rightFirstToeHeight" label="右拇指高度" show-overflow-tooltip />
        <el-table-column prop="rightFootHeight" label="右足背高" show-overflow-tooltip /> -->
        <el-table-column prop="leftFootType" label="足型" show-overflow-tooltip />
        <el-table-column prop="createTime" label="创建时间" show-overflow-tooltip width="100" />
      </el-table>
      <el-pagination v-model:current-page="pagination.current" v-model:page-size="pagination.size"
        :total="pagination.total" @current-change="fetchData" @size-change="fetchData"
        layout="total, sizes, prev, pager, next, jumper" :page-sizes="[10, 20, 50, 100]" class="mb15"
        style="text-align: right;" />
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { fetchFootShapePageByPhone, exportFootShapeByPhone } from '/@/api/anta/FootShapeData'

const searchForm = reactive({
  phone: '',
  deviceCode: '',
  dateRange: [],
  storeName: ''
})

const tableData = ref([])
const pagination = reactive({
  current: 1,
  size: 10,
  total: 0
})

const dateShortcuts = [
  {
    text: '本周',
    value: () => {
      const now = new Date();
      const day = now.getDay() || 7; // 周日为0，设为7
      const start = new Date(now);
      start.setDate(now.getDate() - day + 1);
      const end = new Date(start);
      end.setDate(start.getDate() + 6);
      return [
        start.toISOString().slice(0, 10),
        end.toISOString().slice(0, 10)
      ];
    }
  },
  {
    text: '上周',
    value: () => {
      const now = new Date();
      const day = now.getDay() || 7; // 周日为0，设为7
      // 本周一
      const thisMonday = new Date(now);
      thisMonday.setDate(now.getDate() - day + 1);
      // 上周一
      const lastMonday = new Date(thisMonday);
      lastMonday.setDate(thisMonday.getDate() - 7);
      // 上周日
      const lastSunday = new Date(lastMonday);
      lastSunday.setDate(lastMonday.getDate() + 6);
      return [
        lastMonday.toISOString().slice(0, 10),
        lastSunday.toISOString().slice(0, 10)
      ];
    }
  },
  {
    text: '上个月',
    value: () => {
      const now = new Date();
      const year = now.getMonth() === 0 ? now.getFullYear() - 1 : now.getFullYear();
      const month = now.getMonth() === 0 ? 11 : now.getMonth() - 1;
      const start = new Date(year, month, 1);
      const end = new Date(year, month + 1, 0);
      return [
        start.toISOString().slice(0, 10),
        end.toISOString().slice(0, 10)
      ];
    }
  },
  {
    text: '上一年',
    value: () => {
      const now = new Date();
      const year = now.getFullYear() - 1;
      const start = new Date(year, 0, 1);
      const end = new Date(year, 11, 31);
      return [
        start.toISOString().slice(0, 10),
        end.toISOString().slice(0, 10)
      ];
    }
  }
]

function getQueryParams() {
  return {
    phone: searchForm.phone,
    deviceCode: searchForm.deviceCode,
    storeName: searchForm.storeName,
    startTime: searchForm.dateRange[0] || '',
    endTime: searchForm.dateRange[1] || '',
    current: pagination.current,
    size: pagination.size
  }
}

async function fetchData() {
  try {
    const params = getQueryParams()
    const res = await fetchFootShapePageByPhone(params)
    if (res && res.data) {
      tableData.value = res.data.records
      pagination.total = res.data.total
    }
  } catch (e) {
    ElMessage.error('数据加载失败')
  }
}

function resetForm() {
  searchForm.phone = ''
  searchForm.deviceCode = ''
  searchForm.storeName = ''
  searchForm.dateRange = []
  pagination.current = 1
  fetchData()
}

async function exportData() {
  const params = getQueryParams()
  delete params.current
  delete params.size
  try {
    const res = await exportFootShapeByPhone(params)
    const blob = new Blob([res], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' })
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = '足型数据.xlsx'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(url)
  } catch (e) {
    ElMessage.error('导出失败')
  }
}

onMounted(fetchData)
</script>

<style scoped>
.mb8 {
  margin-bottom: 8px;
}

.mb15 {
  margin-bottom: 15px;
}

.ml10 {
  margin-left: 10px;
}

.layout-padding {
  padding: 20px;
}

.layout-padding-auto {
  width: 100%;
}

.layout-padding-view {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px #f0f1f2;
  padding: 20px;
}
</style>
