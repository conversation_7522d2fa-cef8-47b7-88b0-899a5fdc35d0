import request from '/@/utils/request';

export function fetchList(query?: Object) {
	return request({
		url: '/api/posture/getPostureData',
		method: 'get',
		params: query,
	});
}

export function addObj(obj?: Object) {
	return request({
		url: '/anta/posture',
		method: 'post',
		data: obj,
	});
}

export function getObj(id?: string) {
	return request({
		url: '/anta/posture/' + id,
		method: 'get',
	});
}

export function delObjs(ids?: Object) {
	return request({
		url: '/anta/posture',
		method: 'delete',
		data: ids,
	});
}

export function putObj(obj?: Object) {
	return request({
		url: '/anta/posture',
		method: 'put',
		data: obj,
	});
}

export function delChildObj(ids?: Object) {
	return request({
		url: '/anta/posture/child',
		method: 'delete',
		data: ids,
	});
}
