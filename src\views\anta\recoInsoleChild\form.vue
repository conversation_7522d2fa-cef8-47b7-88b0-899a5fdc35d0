<template>
  <el-dialog :title="form.id ? '编辑' : '新增'" v-model="visible" :close-on-click-modal="false" draggable
    :show-close="false" :width="50 + '%'">
    <el-form ref="dataFormRef" :model="form" :rules="dataRules" formDialogRef label-width="90px" v-loading="loading">
      <el-row :gutter="24">
        <el-col :span="12" class="mb20">
          <el-form-item label="标题" prop="title">
            <el-input v-model="form.title" placeholder="请输入标题" />
          </el-form-item>
        </el-col>


        <el-col :span="12" class="mb20">
          <el-form-item label="类型" prop="type">
            <el-select v-model="form.type" placeholder="请选择类型">
              <el-option label="鞋子" value="shoes"></el-option>
              <el-option label="鞋垫" value="insole"></el-option>
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :span="12" class="mb20">
          <el-form-item label="特征" prop="feature">
            <el-select v-model="form.feature" placeholder="请选择特征">
              <el-option label="正常" value="正常"></el-option>
              <el-option label="低足弓" value="低足弓"></el-option>
              <el-option label="高足弓" value="高足弓"></el-option>
            </el-select>
          </el-form-item>
        </el-col>


        <el-col :span="12" class="mb20">
          <el-form-item label="性别" prop="gender">
            <el-select v-model="form.gender" placeholder="请选择性别">
              <el-option label="男" value="男"></el-option>
              <el-option label="女" value="女"></el-option>
            </el-select>
          </el-form-item>
        </el-col>


        <el-col :span="24" class="mb20">
          <el-form-item label="产品列表" prop="product">
            <div class="product-list-header">
              <el-button type="primary" @click="openProductSelector">选择产品</el-button>
            </div>
          </el-form-item>

          <!-- 产品列表展示 -->
          <el-table :data="productParam" border style="width: 100%; margin-bottom: 15px;">
            <el-table-column prop="code" label="商品编码" min-width="100" />
            <el-table-column prop="name" label="商品名称" min-width="200" />
            <el-table-column prop="amount" label="价格" min-width="80" />
            <el-table-column prop="gender" label="性别" min-width="60">
              <template #default="scope">
                <span>{{ scope.row.gender === 0 || scope.row.gender === '0' ? '男' : scope.row.gender === 1 ||
                  scope.row.gender === '1' ? '女' : '' }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="character" label="特征" min-width="100">
              <template #default="scope">
                <span>{{ formatCharacterDisplay(scope.row.character) }}</span>
              </template>
            </el-table-column>
            <el-table-column label="商品图片" min-width="120">
              <template #default="scope">
                <el-image :src="scope.row.image" fit="contain" style="width: 60px; height: 60px; cursor: pointer;"
                  @click="handlePreviewImage(scope.row.image)">
                  <template #error>
                    <div class="image-slot">
                      <el-icon>
                        <Picture />
                      </el-icon>
                    </div>
                  </template>
                </el-image>
              </template>
            </el-table-column>
            <el-table-column label="操作" min-width="150" fixed="right">
              <template #default="scope">
                <el-button type="primary" size="small" @click="handleEditProduct(scope.row)">
                  编辑
                </el-button>
                <el-button type="danger" size="small" @click="deleteItempro(scope.$index)">
                  删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>

          <!-- <el-button type="primary" @click="handleAddProduct">新增产品</el-button> -->
        </el-col>
      </el-row>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="cancel">取消</el-button>
        <el-button type="primary" @click="onSubmit" :disabled="loading">确认</el-button>
      </span>
    </template>
  </el-dialog>

  <!-- 添加图片预览对话框 -->
  <el-dialog v-model="previewVisible" width="800px" :modal="true" :append-to-body="true" :close-on-click-modal="true"
    :show-close="true" class="preview-dialog" :z-index="3000">
    <div class="preview-container">
      <img :src="previewImage" style="max-width: 100%; max-height: 80vh; object-fit: contain;">
    </div>
  </el-dialog>

  <!-- 产品选择对话框 -->
  <el-dialog v-model="productSelectorVisible" title="选择产品" width="65%" append-to-body>
    <!-- 搜索表单 -->
    <el-form :model="productSearch" :inline="true" class="mb15">
      <el-form-item label="商品编码">
        <el-input v-model="productSearch.code" placeholder="请输入商品编码" clearable style="width: 200px" />
      </el-form-item>
      <el-form-item label="商品名称">
        <el-input v-model="productSearch.name" placeholder="请输入商品名称" clearable style="width: 200px" />
      </el-form-item>
      <el-form-item label="性别">
        <el-select v-model="productSearch.gender" placeholder="请选择性别" clearable style="width: 120px">
          <el-option label="男" :value="0"></el-option>
          <el-option label="女" :value="1"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="特征">
        <el-select v-model="productSearch.character" placeholder="请选择特征" clearable style="width: 120px">
          <el-option label="正常" value="normal"></el-option>
          <el-option label="低足弓" value="lowArch"></el-option>
          <el-option label="高足弓" value="highArch"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="searchProducts">查询</el-button>
        <el-button icon="Refresh" @click="resetProductSearch">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 产品列表 -->
    <el-table :data="productList" v-loading="productListLoading" border height="600"
      @selection-change="handleProductSelectionChange" ref="productTableRef" row-key="code"
      :key="'product-table-' + forceRenderKey">
      <el-table-column type="selection" width="40" align="center" />
      <el-table-column prop="code" label="商品编码" min-width="120" show-overflow-tooltip />
      <el-table-column prop="name" label="商品名称" min-width="180" show-overflow-tooltip />
      <el-table-column prop="amount" label="价格" min-width="80" show-overflow-tooltip />
      <el-table-column prop="gender" label="性别" min-width="60">
        <template #default="scope">
          <span>{{ scope.row.gender === 0 || scope.row.gender === '0' ? '男' : scope.row.gender === 1 || scope.row.gender
            === '1' ? '女' : '' }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="character" label="特征" min-width="100">
        <template #default="scope">
          <span>{{ formatCharacterDisplay(scope.row.character) }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="image" label="商品图片" min-width="120">
        <template #default="scope">
          <el-image :src="scope.row.image" fit="contain" style="width: 60px; height: 60px; cursor: pointer;"
            @click="handlePreviewImage(scope.row.image)">
            <template #error>
              <div class="image-slot">
                <el-icon>
                  <Picture />
                </el-icon>
              </div>
            </template>
          </el-image>
        </template>
      </el-table-column>
    </el-table>

    <div class="pagination-container">
      <el-pagination v-model:current-page="productPagination.currentPage" v-model:page-size="productPagination.pageSize"
        :page-sizes="[10, 20, 50, 100]" background layout="total, sizes, prev, pager, next, jumper"
        :total="productPagination.total" @size-change="handleProductSizeChange"
        @current-change="handleProductCurrentChange" />
    </div>

    <!-- 底部按钮 -->
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="productSelectorVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmProductSelection">确认</el-button>
      </span>
    </template>
  </el-dialog>

  <!-- 产品编辑对话框 -->
  <el-dialog v-model="productEditVisible" :title="isNewProduct ? '新增产品' : '编辑产品'" width="500px">
    <el-form :model="productForm" ref="productFormRef" label-width="100px" :rules="productFormRules">
      <el-form-item label="商品编码" prop="code" required v-if="isNewProduct">
        <el-input v-model="productForm.code" placeholder="请输入商品编码" />
      </el-form-item>
      <el-form-item label="商品名称" prop="name" required>
        <el-input v-model="productForm.name" placeholder="请输入商品名称" />
      </el-form-item>
      <el-form-item label="价格" prop="amount" required>
        <el-input v-model="productForm.amount" placeholder="请输入价格" />
      </el-form-item>
      <el-form-item label="性别" prop="gender" required>
        <el-select v-model="productForm.gender" placeholder="请选择性别">
          <el-option label="男" :value="0"></el-option>
          <el-option label="女" :value="1"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="特征" prop="character" required>
        <el-select v-model="productForm.character" placeholder="请选择特征">
          <el-option label="正常" value="normal"></el-option>
          <el-option label="低足弓" value="lowArch"></el-option>
          <el-option label="高足弓" value="highArch"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="内容列表" prop="content">
        <div v-for="(item, index) in productForm.content || []" :key="index" class="content-item">
          <el-input v-model="item.content" placeholder="请输入内容描述"
            style="width: calc(100% - 80px); margin-right: 10px;" />
          <el-button type="danger" @click="removeProductContent(index)"
            :disabled="(productForm.content || []).length <= 1">删除</el-button>
        </div>
        <div class="content-actions">
          <el-button type="primary" @click="addProductContent"
            :disabled="(productForm.content || []).length >= 5">添加内容</el-button>
          <span class="content-tip" v-if="(productForm.content || []).length >= 5">最多添加5个内容项</span>
        </div>
      </el-form-item>
      <el-form-item label="商品图片" prop="image" required>
        <upload-img v-model:image-url="productForm.image" :drag="true" :file-size="200" />
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="productEditVisible = false">取消</el-button>
        <el-button type="primary" @click="saveProductEdit">确认</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts" name="RecommendDialog">
import { useMessage, useMessageBox } from "/@/hooks/message";
import { getObj1, addObj, putObj } from '/@/api/anta/recommend';
import { Picture } from '@element-plus/icons-vue';
import { fetchShoeTypeSearch } from "/@/api/anta/ShoeType";
import { fetchAddProduct, fetchUpdateProduct, fetchAddDeptAccess } from "/@/api/anta/recommend";

const emit = defineEmits(['refresh']);

// 定义变量内容
const dataFormRef = ref();
const visible = ref(false);
const loading = ref(false);

// 提交表单数据
const form = reactive({
  id: '',
  title: '',
  subTitle: '',
  gender: '',
  product: '',
  introduce: '',
  type: '',
  feature: '',
});

// 产品项目接口定义
interface ProductItem {
  code: string;
  name: string;
  amount: string;
  image: string;
  imgFile?: string;
  deviceId: string[];
  sort: number;
  gender?: string | number;
  character?: string;
  content: { content: string }[];
}

interface IntroduceItem {
  content: string;
}

// 产品相关数据
const productParam = reactive<ProductItem[]>([]);
const introduceParam = reactive<IntroduceItem[]>([]);

// 图片预览相关
const previewVisible = ref(false);
const previewImage = ref('');

// 表格渲染相关，强制重渲染时使用
const forceRenderKey = ref(0);

// 产品选择器相关
const productSelectorVisible = ref(false);
const productList = ref<any[]>([]);
const selectedProducts = ref<any[]>([]);
const selectedProductIds = ref<string[]>([]);
const selectedProductMap = ref<Record<string, any>>({});
const productListLoading = ref(false);
const productTableRef = ref();

// 记录每个页面的选中状态
const pageSelectionMap = ref<Record<number, string[]>>({});

// 产品搜索相关
const productSearch = reactive({
  name: '',
  code: '',
  gender: '' as any,
  character: ''
});

// 产品分页相关
const productPagination = reactive({
  currentPage: 1,
  pageSize: 10,
  total: 0
});

// 产品编辑相关
const productEditVisible = ref(false);
const productForm = ref<ProductItem>({
  code: '',
  name: '',
  amount: '',
  image: '',
  deviceId: [],
  sort: 0,
  gender: '',
  character: '',
  content: [{ content: '' }]
});
const productFormRef = ref();
const editingProductIndex = ref(-1);
const isNewProduct = ref(true);

// 修复表格问题 - 使用Set数据结构来确保选中ID的唯一性和高效操作
const selectedProductSet = ref(new Set<string>());

// 图片预览处理函数
const handlePreviewImage = (imageUrl: string) => {
  previewImage.value = imageUrl;
  previewVisible.value = true;
};

// 打开产品选择器
const openProductSelector = () => {
  // 清空所有选择状态
  selectedProducts.value = [];
  selectedProductIds.value = [];
  selectedProductMap.value = {};
  selectedProductSet.value.clear();
  pageSelectionMap.value = {}; // 清空页面选择记录

  // 设置弹窗和分页
  productSelectorVisible.value = true;
  productPagination.currentPage = 1;

  // 重置搜索条件
  productSearch.name = '';
  productSearch.code = '';

  // 如果已经选择了性别和特征，自动带入搜索条件
  if (form.gender === '男') {
    productSearch.gender = 0;
  } else if (form.gender === '女') {
    productSearch.gender = 1;
  } else {
    productSearch.gender = '';
  }

  // 设置特征
  if (form.feature) {
    switch (form.feature) {
      case '正常':
        productSearch.character = 'normal';
        break;
      case '低足弓':
        productSearch.character = 'lowArch';
        break;
      case '高足弓':
        productSearch.character = 'highArch';
        break;
      default:
        productSearch.character = '';
    }
  } else {
    productSearch.character = '';
  }

  // 执行搜索，确保表格内容被重置
  searchProducts();
};

// 添加特征字段翻译显示函数
const formatCharacterDisplay = (character: string) => {
  if (!character) return '';

  switch (character) {
    case 'normal': return '正常';
    case 'lowArch': return '低足弓';
    case 'highArch': return '高足弓';
    default: return character;
  }
};

// 搜索产品
const searchProducts = async () => {
  try {
    productListLoading.value = true;
    const params: any = {
      code: productSearch.code,
      name: productSearch.name,
      current: productPagination.currentPage,
      size: productPagination.pageSize,
      type: 'insole'
    };

    // 添加性别参数，转换为数字
    if (productSearch.gender !== '' && productSearch.gender !== null && productSearch.gender !== undefined) {
      params.gender = Number(productSearch.gender);
    }

    // 添加特征参数
    if (productSearch.character) {
      params.character = productSearch.character;
    }

    const res = await fetchShoeTypeSearch(params);
    if (res.code === 0) {
      productList.value = res.data.records || [];
      productPagination.total = res.data.total || 0;

      // 恢复选中状态
      ensureSelectionStatus();
    }
    return Promise.resolve();
  } catch (error) {
    useMessage().error('获取产品列表失败');
    return Promise.reject(error);
  } finally {
    productListLoading.value = false;
  }
};

// 确保选中状态在分页切换时保持
const ensureSelectionStatus = () => {
  // 获取当前页码
  const currentPage = productPagination.currentPage;

  // 获取当前页应该选中的ID列表
  const currentPageShouldSelectIds = (pageSelectionMap.value[currentPage] || []).filter(
    id => selectedProductSet.value.has(id) // 确保仍在总选择集中
  );

  // 增加延迟，确保表格DOM已经完全渲染
  setTimeout(() => {
    if (!productTableRef.value) return;

    // 先清除表格所有选中状态
    productTableRef.value.clearSelection();

    // 再分别对每一行进行选中操作
    productList.value.forEach(row => {
      // 使用当前页的选中记录来判断是否应该选中该行
      if (currentPageShouldSelectIds.includes(row.code)) {
        // 选中当前行
        productTableRef.value.toggleRowSelection(row, true);
      }
    });
  }, 300); // 延长延迟时间
};

// 处理产品选择
const handleProductSelectionChange = (selection: any[]) => {
  // 获取当前页码
  const currentPage = productPagination.currentPage;

  // 记录当前页面的选中ID
  pageSelectionMap.value[currentPage] = selection.map(item => item.code);

  // 获取当前页面的所有产品code
  const currentPageProductCodes = new Set(productList.value.map(item => item.code));

  // 从selectedProductSet中移除当前页取消选中的产品
  for (const code of currentPageProductCodes) {
    // 如果当前页面有此产品但不在当前选中列表中，则表示取消选中
    const isCurrentSelected = selection.some(item => item.code === code);
    if (!isCurrentSelected) {
      selectedProductSet.value.delete(code);
      // 同时从记录的映射中删除
      delete selectedProductMap.value[code];
    }
  }

  // 添加当前页新选中的产品
  selection.forEach(product => {
    selectedProductSet.value.add(product.code);
    selectedProductMap.value[product.code] = product;
  });

  // 更新selectedProductIds
  selectedProductIds.value = Array.from(selectedProductSet.value);
};



// 确认产品选择
const confirmProductSelection = () => {
  if (selectedProductSet.value.size === 0) {
    useMessage().error('请至少选择一个产品');
    return;
  }

  // 先清空现有数据，防止重复添加
  const existingCodes = new Set(productParam.map(item => item.code));
  const newProducts: ProductItem[] = [];

  // 将选中的产品添加到productParam中
  Object.values(selectedProductMap.value).forEach(product => {
    // 检查是否已经存在相同code的产品
    if (!existingCodes.has(product.code)) {
      const lastSort = productParam.length > 0 ? productParam[productParam.length - 1].sort : 0;
      newProducts.push({
        code: product.code,
        name: product.name,
        amount: product.amount || '0',
        image: product.image || '',
        deviceId: [],
        sort: lastSort + 1 + newProducts.length,
        gender: product.gender !== undefined ? Number(product.gender) : (form.gender === '男' ? 0 : 1),
        character: product.character || 'normal',
        content: Array.isArray(product.content) ? product.content : [{ content: '' }]
      });
      existingCodes.add(product.code);
    }
  });

  // 添加新产品
  if (newProducts.length > 0) {
    productParam.push(...newProducts);
  }

  productSelectorVisible.value = false;
};

// 新增产品按钮点击事件
const handleAddProduct = () => {
  isNewProduct.value = true;
  productForm.value = {
    code: '',
    name: '',
    amount: '',
    image: '',
    deviceId: [],
    sort: productParam.length + 1,
    gender: '',
    character: '',
    content: [{ content: '' }]
  };
  editingProductIndex.value = -1;
  productEditVisible.value = true;
};

// 编辑产品按钮点击事件
const handleEditProduct = (product: ProductItem) => {
  isNewProduct.value = false;
  const index = productParam.findIndex(item => item.code === product.code);
  if (index !== -1) {
    editingProductIndex.value = index;
    // 处理content字段
    const contentArray = productParam[index].content || [{ content: '' }];

    productForm.value = {
      ...productParam[index],
      content: contentArray
    };
    productEditVisible.value = true;
  }
};

const productFormRules = {
  code: [{ required: true, message: '请输入商品编码', trigger: 'blur' }],
  name: [{ required: true, message: '请输入商品名称', trigger: 'blur' }],
  amount: [{ required: true, message: '请输入价格', trigger: 'blur' }],
  gender: [{ required: true, message: '请选择性别', trigger: 'change' }],
  character: [{ required: true, message: '请选择特征', trigger: 'change' }],
  image: [{ required: true, message: '请上传商品图片', trigger: 'change' }]
};

// 保存产品编辑
const saveProductEdit = async () => {
  // 使用表单验证
  let valid = false;
  try {
    await productFormRef.value.validate();
    valid = true;
  } catch (err) {
    valid = false;
  }

  if (!valid) {
    return;
  }

  try {
    if (isNewProduct.value) {
      // 新增产品到后端
      const params = {
        code: productForm.value.code,
        name: productForm.value.name,
        image: productForm.value.image,
        amount: productForm.value.amount,
        type: 'insole',
        gender: Number(productForm.value.gender),
        character: productForm.value.character,
        content: Array.isArray(productForm.value.content) ? productForm.value.content : [{ content: '' }]
      };

      const res = await fetchAddProduct(params);
      if (res.code === 0) {
        // 添加到本地列表
        productParam.push({
          ...productForm.value,
          deviceId: [],
          sort: productParam.length + 1
        });
        useMessage().success('添加成功');
      } else {
        useMessage().error(res.msg || '添加失败');
        return;
      }
    } else {
      // 更新已有产品
      const params = {
        code: productForm.value.code,
        name: productForm.value.name,
        image: productForm.value.image,
        amount: productForm.value.amount,
        type: 'insole',
        gender: Number(productForm.value.gender),
        character: productForm.value.character,
        content: Array.isArray(productForm.value.content) ? productForm.value.content : [{ content: '' }]
      };

      const res = await fetchUpdateProduct(params);
      if (res.code === 0) {
        // 更新本地列表
        if (editingProductIndex.value !== -1) {
          productParam[editingProductIndex.value] = { ...productForm.value };
        }
        useMessage().success('更新成功');
      } else {
        useMessage().error(res.msg || '更新失败');
        return;
      }
    }

    productEditVisible.value = false;
  } catch (error: any) {
    useMessage().error(error.msg || (isNewProduct.value ? '添加失败' : '更新失败'));
  }
};

// 删除产品
const deleteItempro = async (index: number) => {
  try {
    await useMessageBox().confirm('确定要删除该产品吗？');
    productParam.splice(index, 1);
    // 更新排序
    productParam.forEach((item, idx) => {
      item.sort = idx + 1;
    });
  } catch (error) {
    // 取消删除
  }
};

// 定义校验规则
const dataRules = ref({
  title: [{ required: true, message: '标题不能为空', trigger: 'blur' }],
  gender: [{ required: true, message: '性别不能为空', trigger: 'blur' }],
  type: [{ required: true, message: '类型不能为空', trigger: 'blur' }],
  feature: [{ required: true, message: '特征不能为空', trigger: 'blur' }],
});

// 打开弹窗
const openDialog = (id: string) => {
  visible.value = true;
  form.id = '';

  // 重置表单数据
  nextTick(() => {
    dataFormRef.value?.resetFields();
  });

  // 设置默认type为insole
  form.type = 'insole';

  // 获取recommend信息
  if (id) {
    form.id = id;
    getrecommendData(id);
    productParam.length = 0;
    introduceParam.length = 0;
  }
};

// 提交
const onSubmit = async () => {
  const valid = await dataFormRef.value.validate().catch(() => { });
  if (!valid) return false;

  if (productParam.length === 0) {
    useMessage().error('请至少添加一个产品');
    return false;
  }

  try {
    loading.value = true;

    const productParamJson = JSON.stringify(productParam);
    form.product = productParamJson;
    form.introduce = '[]';

    // 确保type为insole
    form.type = 'insole';

    form.id ? await putObj(form.id, form) : await addObj(form);
    useMessage().success(form.id ? '修改成功' : '添加成功');

    visible.value = false;
    emit('refresh');

    // 重置表单验证状态
    dataFormRef.value.resetFields();
    productParam.length = 0;
    introduceParam.length = 0;
  } catch (err: any) {
    useMessage().error(err.msg);
  } finally {
    loading.value = false;
  }
};

const getrecommendData = async (id: string) => {
  loading.value = true;
  try {
    // 使用getObj1函数获取insole数据，而不是getObj
    const res = await getObj1(id);
    Object.assign(form, res.data);
    const parsedProductParam = JSON.parse(form.product);
    productParam.push(...parsedProductParam);

    // 仍然解析介绍列表数据，以便兼容现有数据，但不再显示在界面上
    if (form.introduce) {
      const parsedIntroduceParam = JSON.parse(form.introduce);
      introduceParam.push(...parsedIntroduceParam);
    }
  } catch (error) {
    useMessage().error('获取数据失败');
  } finally {
    loading.value = false;
  }
};

const cancel = () => {
  visible.value = false; // 关闭对话框
  dataFormRef.value.resetFields(); // 清空表单内容
  productParam.length = 0;
  introduceParam.length = 0;
};

// 重置产品搜索
const resetProductSearch = () => {
  productSearch.name = '';
  productSearch.code = '';
  productSearch.gender = '';
  productSearch.character = '';
  productPagination.currentPage = 1;
  searchProducts();
};

// 处理产品列表分页
const handleProductSizeChange = (size: number) => {
  // 先保存当前页的选中状态
  saveCurrentPageSelection();

  // 更新页面大小并重新加载数据
  productPagination.pageSize = size;
  forceRenderKey.value++;
  searchProducts();
};

const handleProductCurrentChange = (page: number) => {
  // 先保存当前页的选中状态
  saveCurrentPageSelection();

  // 更新页码并重新加载数据
  productPagination.currentPage = page;
  forceRenderKey.value++;
  searchProducts();
};

// 保存当前页面的选中状态
const saveCurrentPageSelection = () => {
  const currentPage = productPagination.currentPage;
  const currentSelection = productTableRef.value?.getSelection?.() || [];
  pageSelectionMap.value[currentPage] = currentSelection.map((item: any) => item.code);
};

// 添加产品内容项
const addProductContent = () => {
  if (!productForm.value.content) {
    productForm.value.content = [];
  }

  if (productForm.value.content.length < 5) {
    productForm.value.content.push({ content: '' });
  } else {
    useMessage().wraning('最多添加5个内容项');
  }
};

// 删除产品内容项
const removeProductContent = (index: number) => {
  if (productForm.value.content && productForm.value.content.length > 1) {
    productForm.value.content.splice(index, 1);
  } else {
    useMessage().wraning('至少保留一项内容');
  }
};

// 暴露变量
defineExpose({
  openDialog
});
</script>

<style lang="scss" scoped>
.preview-image {
  height: 100px;
  width: 100px;
  margin-top: -10px;
}

.product-list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.pagination-container {
  margin-top: 15px;
  display: flex;
  justify-content: flex-end;
}

.preview-container {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  min-height: 400px;
}

.image-slot {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  background: #f5f7fa;
  color: #909399;
  font-size: 20px;
}

:deep(.preview-dialog) {
  .el-dialog__body {
    padding: 10px;
  }
}

.avatar-uploader {
  :deep() {
    .avatar {
      width: 180px;
      height: 125px;
      display: block;
    }

    .el-upload {
      border: 1px dashed var(--el-border-color);
      border-radius: 6px;
      cursor: pointer;
      position: relative;
      overflow: hidden;
      transition: var(--el-transition-duration-fast);
    }

    .el-upload:hover {
      border-color: var(--el-color-primary);
    }

    .el-icon.avatar-uploader-icon {
      font-size: 28px;
      color: #8c939d;
      width: 150px;
      height: 125px;
      text-align: center;
    }
  }
}

.device-id-item {
  margin-top: 10px;
  margin-bottom: 10px;

  :deep(.el-select) {
    width: 150px !important;

    .el-select__tags {
      max-height: 36px;
      overflow-x: hidden;
      flex-wrap: nowrap;
      white-space: nowrap;
      padding-left: 8px;
      margin-left: 4px;

      .el-tag {
        display: inline-flex;
        max-width: 80px;
        margin-right: 4px;
        margin-left: 2px;
      }

      .el-select__tags-text {
        display: inline-block;
        max-width: 60px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }

    .el-input__wrapper {
      height: 36px;
      padding: 0 8px;
    }

    .el-select__tags-collapse-tags {
      display: inline-flex;
      align-items: center;
      max-width: 60px;
      margin-left: 4px;

      .el-tag {
        display: inline-flex;
        max-width: 60px;

        .el-tag__content {
          overflow: hidden;
          text-overflow: ellipsis;
        }
      }
    }
  }
}
</style>