import request from '/@/utils/request';

export function fetchList(query?: Object) {
	return request({
		url: '/api/bodyFat/getBodyFatData',
		method: 'get',
		params: query,
	});
}

export function addObj(obj?: Object) {
	return request({
		url: '/anta/bodyFat',
		method: 'post',
		data: obj,
	});
}

export function getObj(id?: string) {
	return request({
		url: '/anta/bodyFat/' + id,
		method: 'get',
	});
}

export function delObjs(ids?: Object) {
	return request({
		url: '/anta/bodyFat',
		method: 'delete',
		data: ids,
	});
}

export function putObj(obj?: Object) {
	return request({
		url: '/anta/bodyFat',
		method: 'put',
		data: obj,
	});
}

export function delChildObj(ids?: Object) {
	return request({
		url: '/anta/bodyFat/child',
		method: 'delete',
		data: ids,
	});
}
