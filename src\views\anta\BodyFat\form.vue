<template>
  <el-drawer :title="form.id ? (detail ? '详情' : '编辑') : '添加'" v-model="visible" size="50%">
      <el-form ref="dataFormRef" :model="form" :rules="dataRules" label-width="90px" :disabled="detail" v-loading="loading">
        <el-row :gutter="24">
    <el-col :span="12" class="mb20">
      <el-form-item label="名字" prop="name">
        <el-input v-model="form.name"/>
      </el-form-item>
      </el-col>
    <el-col :span="12" class="mb20">
      <el-form-item label="年龄" prop="age">
        <el-input v-model="form.age"/>
      </el-form-item>
      </el-col>
    <el-col :span="12" class="mb20">
      <el-form-item label="性别" prop="gender">
        <el-input v-model="form.gender"/>
      </el-form-item>
    </el-col>
    <el-col :span="12" class="mb20">
      <el-form-item label="创建时间" prop="createTime">
        <el-input v-model="form.createTime"/>
      </el-form-item>
      </el-col>
    <el-col :span="12" class="mb20">
      <el-form-item label="身高" prop="height">
        <el-input v-model="form.height"/>
      </el-form-item>
      </el-col>
    <el-col :span="12" class="mb20">
      <el-form-item label="体重" prop="weight">
        <el-input v-model="form.weight" />
      </el-form-item>
      </el-col>
    <el-col :span="12" class="mb20">
      <el-form-item label="bmi" prop="bmi">
        <el-input v-model="form.bmi"/>
      </el-form-item>
      </el-col>
    <el-col :span="12" class="mb20">
      <el-form-item label="体脂率" prop="bfr">
        <el-input v-model="form.bfr" />
      </el-form-item>
      </el-col>
    <el-col :span="12" class="mb20">
      <el-form-item label="水分率" prop="bwr">
        <el-input v-model="form.bwr"/>
      </el-form-item>
    </el-col>
    <el-col :span="12" class="mb20">
      <el-form-item label="代谢率" prop="bmr">
        <el-input v-model="form.bmr" />
      </el-form-item>
    </el-col>
    <el-col :span="12" class="mb20">
      <el-form-item label="肥胖度" prop="bod">
        <el-input v-model="form.bod" />
      </el-form-item>
      </el-col>
    <el-col :span="12" class="mb20">
      <el-form-item label="骨含量" prop="bmc">
        <el-input v-model="form.bmc" />
      </el-form-item>
      </el-col>
    <el-col :span="12" class="mb20">
      <el-form-item label="肌肉含量" prop="slm">
        <el-input v-model="form.slm" />
      </el-form-item>
      </el-col>

    </el-row>
  <el-row :gutter="24">
  </el-row>
      </el-form>
      <!-- <template #footer>
        <span class="dialog-footer">
          <el-button @click="visible = false">取消</el-button>
          <el-button type="primary" @click="onSubmit" :disabled="loading">确认</el-button>
        </span>
      </template> -->
    </el-drawer>
</template>

<script setup lang="ts" name="BodyFatDialog">
// import { useDict } from '/@/hooks/dict';
// import { rule } from '/@/utils/validate';
import { useMessage } from "/@/hooks/message";
// import { useI18n } from 'vue-i18n';
import { getObj, addObj, putObj, delChildObj } from '/@/api/anta/BodyFat'
// const scFormTable = defineAsyncComponent(() => import('/@/components/FormTable/index.vue'));
const emit = defineEmits(['refresh']);

// const { t } = useI18n();

// 定义变量内容
const dataFormRef = ref();
const visible = ref(false);
const loading = ref(false);
const detail = ref(false);

// 定义字典

// 提交表单数据
const form = reactive({
    id:'',
    name: '',
    age: '',
    gender: '',
    createTime: '',
	  height: '',
	  weight: '',
	  bmi: '',
	  bfr: '',
	  bod: '',
	  bmc: '',
	  bwr: '',
	  slm: '',
	  bmr: '',
	  atMeasureList:[],
});


// 定义校验规则
const dataRules = ref({
        measureId: [{required: true, message: '测量Id不能为空', trigger: 'blur'}],
        deviceCode: [{required: true, message: '设备编码不能为空', trigger: 'blur'}],
        height: [{required: true, message: '身高不能为空', trigger: 'blur'}],
        weight: [{required: true, message: '体重不能为空', trigger: 'blur'}],
        bodyShape: [{required: true, message: '身形：瘦，偏瘦，正常，超重，肥胖之一不能为空', trigger: 'blur'}],
        bmi: [{required: true, message: 'bmi不能为空', trigger: 'blur'}],
        bfr: [{required: true, message: '体脂率不能为空', trigger: 'blur'}],
        bod: [{required: true, message: '肥胖度不能为空', trigger: 'blur'}],
        bmc: [{required: true, message: '骨含量不能为空', trigger: 'blur'}],
        bwr: [{required: true, message: '水分率不能为空', trigger: 'blur'}],
        slm: [{required: true, message: '肌肉含量不能为空', trigger: 'blur'}],
        bmr: [{required: true, message: '基础代谢率不能为空', trigger: 'blur'}],
})

// 打开弹窗
const openDialog = (id: string, isDetail: boolean) => {
  visible.value = true
  detail.value = isDetail
  form.id = ''

  // 重置表单数据
  nextTick(() => {
    dataFormRef.value?.resetFields();
    form.atMeasureList = [];
  });

  // 获取bodyFat信息
  if (id) {
    form.id = id
    getBodyFatData(id)
  }
  // getMenuData();
};

// 提交
// const onSubmit = async () => {
//   const valid = await dataFormRef.value.validate().catch(() => {});
//   if (!valid) return false;

//   try {
//     loading.value = true;
//     form.id ? await putObj(form) : await addObj(form);
//     useMessage().success(form.id ? '修改成功' : '添加成功');
//     visible.value = false;
//     emit('refresh');
//   } catch (err: any) {
//     useMessage().error(err.msg);
//   } finally {
//     loading.value = false;
//   }
// };
// 删除子表数据
const deleteChild = async (obj: { id: string }) => {
  if (obj.id) {
    try {
      await delChildObj([obj.id]);
      useMessage().success('删除成功');
    } catch (err: any) {
      useMessage().error(err.msg);
    }
  }
};

// 初始化表单数据
const getBodyFatData = (id: string) => {
  // 获取数据
  getObj(id).then((res: any) => {
    Object.assign(form, res.data)
  })
};


// 暴露变量
defineExpose({
  openDialog
});
</script>