import request from '/@/utils/request';

export function fetchListshoes(query?: Object) {
	return request({
		url: '/anta/recommend/getshoesData',
		method: 'get',
		params: query,
	});
}
export function fetchListProducts(query?: Object) {
	return request({
		url: '/anta/recommend/products',
		method: 'get',
		params: query,
	});
}

export function fetchUpdateDeviceId(data: Object) {
	return request({
		url: '/anta/recommend/updateDeviceId',
		method: 'post',
		data,
	});
}

export function fetchUpdateProduct(data: Object) {
	return request({
		url: '/anta/recommend/updateProduct',
		method: 'post',
		data,
	});
}

export function fetchAddProduct(data: Object) {
	return request({
		url: '/anta/recommend/addProduct',
		method: 'post',
		data,
	});
}

export function fetchDeleteProduct(data: Object) {
	return request({
		url: '/anta/recommend/deleteProduct',
		method: 'post',
		data,
	});
}

export function fetchBatchDeleteProducts(codes: string[], type: string = 'shoes') {
	return request({
		url: '/anta/recommend/batchDeleteProducts',
		method: 'post',
		data: codes,
		params: { type },
	});
}

export function fetchListinsole(query?: Object) {
	return request({
		url: '/anta/recommend/getinsoleData',
		method: 'get',
		params: query,
	});
}

export function fetchSearch(query?: Object) {
	return request({
		url: '/anta/recommend/products/search',
		method: 'get',
		params: query,
	});
}

export function addObj(obj?: Object) {
	return request({
		url: '/anta/recommend/save',
		method: 'post',
		data: obj,
	});
}

export function getObj(id?: string) {
	return request({
		url: '/anta/recommend/shoes/' + id,
		method: 'get',
	});
}

export function getObj1(id?: string) {
	return request({
		url: '/anta/recommend/insole/' + id,
		method: 'get',
	});
}

export function delObjs(ids?: Object) {
	return request({
		url: '/anta/recommend/del',
		method: 'delete',
		data: ids,
	});
}

export function putObj(id?: string, obj?: Object) {
	return request({
		url: '/anta/recommend/update/' + id,
		method: 'post',
		data: obj,
	});
}

export function uploadObj(obj?: Object) {
	return request({
		url: '/anta/recommend/upload',
		method: 'post',
		data: obj,
	});
}

// 商品导出接口，支持多条件筛选，返回Excel文件流
export function fetchExportProduct(params?: Object) {
	return request({
		url: '/anta/recommend/export',
		method: 'get',
		params,
		responseType: 'blob',
	});
}

// 商品导入接口，批量导入Excel，返回每条数据的导入结果
export function fetchImportProduct(data: FormData) {
	return request({
		url: '/anta/recommend/import',
		method: 'post',
		data,
		headers: { 'Content-Type': 'multipart/form-data' },
	});
}

// 下载商品导入模板
export function fetchDownloadTemplate() {
	return request({
		url: '/anta/recommend/import/template',
		method: 'get',
		responseType: 'blob',
	});
}

// 新增接口：区域部门选择产品（建立使用权限关联）
export function fetchAddDeptAccess(data: Object) {
	return request({
		url: '/anta/recommend/addDeptAccess',
		method: 'post',
		data,
	});
}

/**
 * 获取单设备用户列表
 * @returns
 */
export const fetchSingleDeviceUsers = () => {
	return request({
		url: '/anta/recommend/getSingleDeviceUsers',
		method: 'get',
	});
};
