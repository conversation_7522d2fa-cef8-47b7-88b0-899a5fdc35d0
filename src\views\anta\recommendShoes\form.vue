<template>
  <el-dialog :title="form.id ? '编辑' : '新增'" v-model="visible" :close-on-click-modal="false" draggable
    :show-close="false" :width="1000">
    <el-form ref="dataFormRef" :model="form" :rules="dataRules" formDialogRef label-width="90px" v-loading="loading">
      <el-row :gutter="24">
        <el-col :span="12" class="mb20">
          <el-form-item label="标题" prop="title">
            <el-input v-model="form.title" placeholder="请输入标题" />
          </el-form-item>
        </el-col>


        <el-col :span="12" class="mb20">
          <el-form-item label="类型" prop="type">
            <el-select v-model="form.type" placeholder="请选择类型">
              <el-option label="鞋子" value="shoes"></el-option>
              <el-option label="鞋垫" value="insole"></el-option>
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :span="12" class="mb20">
          <el-form-item label="特征" prop="feature">
            <el-select v-model="form.feature" placeholder="请选择特征">
              <el-option label="大体重" value="大体重"></el-option>
              <el-option label="正常" value="正常"></el-option>
              <el-option label="低足弓" value="低足弓"></el-option>
              <el-option label="高足弓" value="高足弓"></el-option>
            </el-select>
          </el-form-item>
        </el-col>


        <el-col :span="12" class="mb20">
          <el-form-item label="性别" prop="gender">
            <el-select v-model="form.gender" placeholder="请选择性别">
              <el-option label="男" value="0"></el-option>
              <el-option label="女" value="1"></el-option>
            </el-select>
          </el-form-item>
        </el-col>


        <el-col :span="12" class="mb20">
          <el-form-item label="产品列表" prop="product">
          </el-form-item>

          <div v-for="(item, index) in productParam" :key="index">
            <el-form-item label='商品编码'>
              <el-input v-model="item.code" style="width:150px" />
            </el-form-item>
            <el-form-item label='商品名称' style="margin-left: 230px;margin-top: -50px;">
              <el-input v-model="item.name" />
            </el-form-item>
            <el-form-item label='商品价格'>
              <el-input v-model="item.amount" style="width:150px" />
            </el-form-item>

            <!-- <el-form-item label='设备编码' class="device-id-item">
              <el-select v-model="item.deviceId" multiple filterable allow-create default-first-option :max="20"
                placeholder="请输入设备编码" style="width: 150px;" collapse-tags collapse-tags-tooltip>
                <el-option v-for="tag in item.deviceId" :key="tag" :label="tag" :value="tag" />
              </el-select>
            </el-form-item> -->

            <el-form-item label='商品图片' style="margin-left: 230px;margin-top: -50px;width:240px;">
              <el-upload ref="uploadRef" :auto-upload="false" class="avatar-uploader" :show-file-list="false"
                :on-change="file => onSelectFile(item, file)" :action="item.image">
                <img v-if="item.imgFile" :src="item.imgFile" class="avatar" />
                <img v-else-if="item.image" :src="item.image" />
                <el-icon v-else class="avatar-uploader-icon">
                  <Plus />
                </el-icon>
              </el-upload>

            </el-form-item>

            <el-button type="danger" @click="deleteItempro(index)" style="margin-bottom: 5px;">删除</el-button>
            <el-divider />
          </div>

          <el-button type="primary" @click="addNewItempro">新增</el-button>

        </el-col>

        <el-col :span="12" class="mb20">
          <el-form-item label="介绍列表" prop="introduce">
            <!-- <el-input v-model="form.introduce" placeholder="请输入介绍列表"/> -->
          </el-form-item>

          <div v-for="(item, index) in introduceParam" :key="index">
            <el-form-item style="padding: 5px 0;">
              <el-input v-model="item.content" />
            </el-form-item>
            <el-button type="danger" @click="deleteItemint(index)" style="margin-bottom: 15px;">删除</el-button>
          </div>

          <el-button type="primary" @click="addNewItemint">新增</el-button>

        </el-col>


      </el-row>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="cancel">取消</el-button>
        <el-button type="primary" @click="onSubmit" :disabled="loading">确认</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts" name="RecommendDialog">
import { useDict } from '/@/hooks/dict';
import { useMessage } from "/@/hooks/message";
import { getObj, addObj, putObj, uploadObj, fetchSearch } from '/@/api/anta/recommend'
import { rule } from '/@/utils/validate';

const emit = defineEmits(['refresh']);

// 定义变量内容
const dataFormRef = ref();
const visible = ref(false)
const loading = ref(false)
// 定义字典
const { gender } = useDict('gender')


// 提交表单数据
const form = reactive({
  id: '',
  title: '',
  subTitle: '',
  gender: '',
  product: '',
  introduce: '',
  type: '',
  feature: '',
});

interface ProductItem {
  code: string;
  name: string;
  amount: string;
  image: string;
  imgFile?: string;
  deviceId: string[];
  sort: number;
}

interface IntroduceItem {
  content: string;
}

const productParam = reactive<ProductItem[]>([]);
const introduceParam = reactive<IntroduceItem[]>([]);

const uploadRef = ref()

const onSelectFile = (item: ProductItem, file: any) => {
  const reader = new FileReader();
  reader.onload = (e: ProgressEvent<FileReader>) => {
    item.image = e.target?.result as string;

    const formData = new FormData();
    formData.append('file', file.raw);
    uploadObj(formData).then(response => {
      item.image = response.data.url;
    })
  };
  reader.readAsDataURL(file.raw);
}

// 新增商品
const addNewItempro = () => {
  const lastSort = productParam.length > 0 ? productParam[productParam.length - 1].sort : 0;
  const newItem: ProductItem = {
    code: '',
    name: '',
    amount: '',
    image: '',
    deviceId: [],
    sort: lastSort + 1
  };
  productParam.push(newItem);
};
//删除商品
const deleteItempro = (index: number) => {
  productParam.splice(index, 1);
};
// 新增介绍
const addNewItemint = () => {
  const newItem: IntroduceItem = { content: '' };
  introduceParam.push(newItem);
};
//删除介绍
const deleteItemint = (index: number) => {
  introduceParam.splice(index, 1);
};

// 定义校验规则
const dataRules = ref({
  title: [{ required: true, message: '标题不能为空', trigger: 'blur' }],
  gender: [{ required: true, message: '性别不能为空', trigger: 'blur' }],
  // product: [{ required: true, message: '产品列表不能为空', trigger: 'blur' }],
  // introduce: [{ required: true, message: '介绍列表不能为空', trigger: 'blur' }],
  type: [{ required: true, message: '类型不能为空', trigger: 'blur' }],
  feature: [{ required: true, message: '特征不能为空', trigger: 'blur' }],
})

// 打开弹窗
const openDialog = (id: string) => {
  visible.value = true
  form.id = ''

  // 重置表单数据
  nextTick(() => {
    dataFormRef.value?.resetFields();
  });

  // 获取recommend信息
  if (id) {
    form.id = id
    getrecommendData(id);
    productParam.length = 0;
    introduceParam.length = 0;
  }
};


// 提交
const onSubmit = async () => {
  const valid = await dataFormRef.value.validate().catch(() => { });
  if (!valid) return false;

  try {
    loading.value = true;

    const productParamJson = JSON.stringify(productParam);
    const introduceParamJson = JSON.stringify(introduceParam);
    form.product = productParamJson;
    form.introduce = introduceParamJson;
    // form.files = files
    console.log(form);
    form.id ? await putObj(form.id, form) : await addObj(form);
    useMessage().success(form.id ? '修改成功' : '添加成功');

    visible.value = false;
    emit('refresh');

    // 重置表单验证状态
    dataFormRef.value.resetFields();
    productParam.length = 0;
    introduceParam.length = 0;
  } catch (err: any) {
    useMessage().error(err.msg);
  } finally {
    loading.value = false;
  }
};

const getrecommendData = async (id: string) => {
  loading.value = true;
  try {
    const res = await getObj(id);
    Object.assign(form, res.data);
    const parsedProductParam = JSON.parse(form.product);
    productParam.push(...parsedProductParam);

    const parsedIntroduceParam = JSON.parse(form.introduce);
    introduceParam.push(...parsedIntroduceParam);

  } catch (error) {
    console.log('Error occurred while getting data:', error);
  } finally {
    loading.value = false;
  }
};
const cancel = () => {
  visible.value = false; // 关闭对话框
  dataFormRef.value.resetFields(); // 清空表单内容
  productParam.length = 0;
  introduceParam.length = 0;
};

// 暴露变量
defineExpose({
  openDialog
});
</script>

<style lang="scss" scoped>
.preview-image {
  height: 100px;
  width: 100px;
  margin-top: -10px;
}

.upload-icon {
  font-size: 50px;
}

.upload-image-container {
  position: relative;
  width: 2%;
  height: 200px;
}

.upload-image-container .el-icon-plus {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.avatar-uploader {
  :deep() {
    .avatar {
      width: 180px;
      height: 125px;
      display: block;
    }

    .el-upload {
      border: 1px dashed var(--el-border-color);
      border-radius: 6px;
      cursor: pointer;
      position: relative;
      overflow: hidden;
      transition: var(--el-transition-duration-fast);
    }

    .el-upload:hover {
      border-color: var(--el-color-primary);
    }

    .el-icon.avatar-uploader-icon {
      font-size: 28px;
      color: #8c939d;
      width: 150px;
      height: 125px;
      text-align: center;
    }
  }
}

.device-id-item {
  margin-top: 10px;
  margin-bottom: 10px;

  :deep(.el-select) {
    width: 150px !important;

    .el-select__tags {
      max-height: 36px;
      overflow-x: hidden;
      flex-wrap: nowrap;
      white-space: nowrap;
      padding-left: 8px;
      margin-left: 4px;

      .el-tag {
        display: inline-flex;
        max-width: 80px;
        margin-right: 4px;
        margin-left: 2px;
      }

      .el-select__tags-text {
        display: inline-block;
        max-width: 60px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }

    .el-input__wrapper {
      height: 36px;
      padding: 0 8px;
    }

    .el-select__tags-collapse-tags {
      display: inline-flex;
      align-items: center;
      max-width: 60px;
      margin-left: 4px;

      .el-tag {
        display: inline-flex;
        max-width: 60px;

        .el-tag__content {
          overflow: hidden;
          text-overflow: ellipsis;
        }
      }
    }
  }
}
</style>