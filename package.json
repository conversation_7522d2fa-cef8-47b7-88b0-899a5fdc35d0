{"name": "codelink-ui", "version": "5.2.0", "description": "微服务开发平台", "author": "codelink", "license": "商业版权", "scripts": {"dev": "vite --force", "build": "cross-env NODE_OPTIONS=--max-old-space-size=4096 vite build", "build:docker": "cross-env NODE_OPTIONS=--max-old-space-size=4096 vite build --outDir ./docker/dist/", "lint:eslint": "eslint --fix --ext .js,.ts,.vue ./src", "prettier": "prettier --write ."}, "dependencies": {"@chenfengyuan/vue-qrcode": "^2.0.0", "@element-plus/icons-vue": "^2.0.10", "@vueuse/core": "^10.4.1", "@wangeditor/editor": "5.1.23", "@wangeditor/editor-for-vue": "5.1.12", "autoprefixer": "^10.4.7", "axios": "^1.3.3", "china-area-data": "^5.0.1", "codemirror": "5.65.5", "crypto-js": "^3.1.9-1", "driver.js": "^0.9.8", "echarts": "^5.4.1", "element-plus": "2.3.1", "form-designer-plus": "^0.1.5", "js-cookie": "^3.0.1", "lodash": "^4.17.21", "mitt": "^3.0.0", "nprogress": "^0.2.0", "pinia": "^2.0.32", "qrcode": "1.5.1", "qs": "^6.11.0", "screenfull": "^6.0.2", "sm-crypto": "^0.3.12", "sortablejs": "^1.15.0", "splitpanes": "^3.1.5", "tailwindcss": "^3.0.24", "vue": "^3.2.47", "vue-clipboard3": "^2.0.0", "vue-echarts": "^6.2.3", "vue-i18n": "9.2.2", "vue-router": "^4.1.6", "vue3-ts-jsoneditor": "2.8.6", "vuedraggable": "^4.1.0"}, "devDependencies": {"@types/node": "^18.14.0", "@types/nprogress": "^0.2.0", "@types/sortablejs": "^1.15.0", "@typescript-eslint/eslint-plugin": "^5.53.0", "@typescript-eslint/parser": "^5.53.0", "@vitejs/plugin-vue": "^4.0.0", "@vue/compiler-sfc": "^3.2.47", "consola": "^2.15.3", "cross-env": "7.0.3", "esbuild": "^0.23.0", "eslint": "^8.34.0", "eslint-plugin-vue": "^9.9.0", "pinia-plugin-persist": "^1.0.0", "prettier": "2.8.4", "sass": "^1.58.3", "typescript": "^4.9.5", "unplugin-auto-import": "^0.13.0", "vite": "^4.3.3", "vite-plugin-compression": "^0.5.1", "vite-plugin-style-import": "^2.0.0", "vite-plugin-top-level-await": "^1.3.0", "vite-plugin-vue-setup-extend": "^0.4.0", "vue-eslint-parser": "^9.1.0"}, "browserslist": ["> 1%", "last 2 versions", "not dead"], "bugs": {"url": "https://codelink.com"}, "engines": {"node": ">=16.0.0", "npm": ">= 7.0.0"}, "keywords": ["vue", "vue3", "vuejs/vue-next", "element-ui", "element-plus"], "repository": {"type": "git", "url": "https://gitee.com/log4j/pig"}}