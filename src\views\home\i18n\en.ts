export default {
	home: {
		addFavoriteRoutesTip: 'no data , right click on the tab to favorite',
		quickNavigationToolsTip: 'quick navigation bar',
		systemLogsTip: 'system logs',
		auditLogsTip: 'audit logs',
		moreTip: 'more',
		newsletterTip: 'news letter',
		pendingTask: 'pending task✍️',
		copyTask: 'copy task🔖',
	},
	schedule: {
		index: '#',
		importsysScheduleTip: 'import SysSchedule',
		id: 'id',
		title: 'title',
		type: 'type',
		state: 'state',
		content: 'content',
		time: 'time',
		date: 'date',
		createBy: 'createBy',
		createTime: 'createTime',
		updateBy: 'updateBy',
		updateTime: 'updateTime',
		delFlag: 'delFlag',
		tenantId: 'tenantId',
		inputIdTip: 'input id',
		inputTitleTip: 'input title',
		inputTypeTip: 'input type',
		inputStateTip: 'input state',
		inputContentTip: 'input content',
		inputTimeTip: 'input time',
		inputDateTip: 'input date',
		inputCreateByTip: 'input createBy',
		inputCreateTimeTip: 'input createTime',
		inputUpdateByTip: 'input updateBy',
		inputUpdateTimeTip: 'input updateTime',
		inputDelFlagTip: 'input delFlag',
		inputTenantIdTip: 'input tenantId',
	},
};
