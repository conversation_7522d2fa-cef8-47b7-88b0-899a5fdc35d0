export default {
	wxFansMsg: {
		index: '#',
		importwxMsgTip: 'import WxMsg',
		id: 'id',
		appName: 'appName',
		appLogo: 'appLogo',
		wxUserId: 'wxUserId',
		nickName: 'nickName',
		headimgUrl: 'headimgUrl',
		type: 'type',
		repType: 'repType',
		repEvent: 'repEvent',
		repContent: 'repContent',
		repMediaId: 'repMediaId',
		repName: 'repName',
		repDesc: 'repDesc',
		repUrl: 'repUrl',
		repHqUrl: 'repHqUrl',
		content: 'content',
		repThumbMediaId: 'repThumbMediaId',
		repThumbUrl: 'repThumbUrl',
		repLocationX: 'repLocationX',
		repLocationY: 'repLocationY',
		repScale: 'repScale',
		readFlag: 'readFlag',
		appId: 'appId',
		openId: 'openId',
		remark: 'remark',
		delFlag: 'delFlag',
		createTime: 'createTime',
		updateTime: 'updateTime',
		tenantId: 'tenantId',
		inputIdTip: 'input id',
		inputAppNameTip: 'input appName',
		inputAppLogoTip: 'input appLogo',
		inputWxUserIdTip: 'input wxUserId',
		inputNickNameTip: 'input nickName',
		inputHeadimgUrlTip: 'input headimgUrl',
		inputTypeTip: 'input type',
		inputRepTypeTip: 'input repType',
		inputRepEventTip: 'input repEvent',
		inputRepContentTip: 'input repContent',
		inputRepMediaIdTip: 'input repMediaId',
		inputRepNameTip: 'input repName',
		inputRepDescTip: 'input repDesc',
		inputRepUrlTip: 'input repUrl',
		inputRepHqUrlTip: 'input repHqUrl',
		inputContentTip: 'input content',
		inputRepThumbMediaIdTip: 'input repThumbMediaId',
		inputRepThumbUrlTip: 'input repThumbUrl',
		inputRepLocationXTip: 'input repLocationX',
		inputRepLocationYTip: 'input repLocationY',
		inputRepScaleTip: 'input repScale',
		inputReadFlagTip: 'input readFlag',
		inputAppIdTip: 'input appId',
		inputOpenIdTip: 'input openId',
		inputRemarkTip: 'input remark',
		inputDelFlagTip: 'input delFlag',
		inputCreateTimeTip: 'input createTime',
		inputUpdateTimeTip: 'input updateTime',
		inputTenantIdTip: 'input tenantId',
	},
};
