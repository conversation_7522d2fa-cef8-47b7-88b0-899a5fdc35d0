<template>
  <div class="layout-padding">
    <div class="layout-padding-auto layout-padding-view">
      <el-row>
        <div class="mb8" style="width: 100%">
          <el-button icon="folder-add" type="primary" class="ml10" @click="formDialogRef.openDialog()">
            新 增
          </el-button>
          <el-button plain :disabled="multiple" icon="Delete" type="primary" @click="handleDelete(selectObjs)">
            删除
          </el-button>
          <right-toolbar v-model:showSearch="showSearch" :export="'anta_recommend_export'" @exportExcel="exportExcel"
            class="ml10 mr20" style="float: right;" @queryTable="getDataList"></right-toolbar>
        </div>
      </el-row>
      <el-table :data="state.dataList" v-loading="state.loading" border :cell-style="tableStyle.cellStyle"
        :header-cell-style="tableStyle.headerCellStyle" @selection-change="selectionChangHandle"
        @sort-change="sortChangeHandle">
        <el-table-column type="selection" width="40" align="center" />
        <el-table-column type="index" label="id" width="50" />
        <el-table-column prop="title" label="标题" show-overflow-tooltip />
        <el-table-column prop="type" label="类型" show-overflow-tooltip />
        <el-table-column prop="feature" label="特征" show-overflow-tooltip />
        <el-table-column prop="gender" label="性别" show-overflow-tooltip />

        <el-table-column label="操作" width="200">
          <template #default="scope">
            <el-button text type="primary" icon="view" @click="detailDialogRef.openDialog(scope.row.id, true)">
              详情
            </el-button>
            <el-button icon="edit-pen" text type="primary"
              @click="formDialogRef.openDialog(scope.row.id)">编辑</el-button>
            <el-button icon="delete" text type="primary" @click="handleDelete([scope.row.id])">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <pagination @size-change="sizeChangeHandle" @current-change="currentChangeHandle" v-bind="state.pagination" />
    </div>

    <!-- 编辑、新增  -->
    <form-dialog ref="formDialogRef" @refresh="getDataList(false)" />
    <detail-dialog ref="detailDialogRef" @refresh="getDataList(false)" />

  </div>
</template>

<script setup lang="ts" name="systemRecommend">
import { BasicTableProps, useTable } from "/@/hooks/table";
import { fetchListinsole, delObjs } from "/@/api/anta/recommend";
import { useMessage, useMessageBox } from "/@/hooks/message";
import { useDict } from '/@/hooks/dict';

// 引入组件
const FormDialog = defineAsyncComponent(() => import('./form.vue'));
const DetailDialog = defineAsyncComponent(() => import('./detail.vue'));
// 定义查询字典

const { gender } = useDict('gender')
// 定义变量内容
const formDialogRef = ref()

const detailDialogRef = ref()
// 搜索变量
const queryRef = ref()
const showSearch = ref(true)
// 多选变量
const selectObjs = ref([]) as any
const multiple = ref(true)
const id = '';

const state: BasicTableProps = reactive<BasicTableProps>({
  queryForm: {},
  pageList: fetchListinsole
})

//  table hook
const {
  getDataList,
  currentChangeHandle,
  sizeChangeHandle,
  sortChangeHandle,
  downBlobFile,
  tableStyle
} = useTable(state)

// 清空搜索条件
const resetQuery = () => {
  // 清空搜索条件
  queryRef.value?.resetFields()
  // 清空多选
  selectObjs.value = []
  getDataList()
}

// 导出excel
const exportExcel = () => {
  downBlobFile('/anta/recommend/export', Object.assign(state.queryForm, { ids: selectObjs }), 'recommend.xlsx')
}

// 多选事件
const selectionChangHandle = (objs: { id: string }[]) => {
  selectObjs.value = objs.map(({ id }) => id);
  multiple.value = !objs.length;
};

// 删除操作
const handleDelete = async (ids: string[]) => {
  try {
    await useMessageBox().confirm('此操作将永久删除');
  } catch {
    return;
  }

  try {
    await delObjs(ids); // 直接将ids数组发送给后端，而不是将每个id包装成对象
    getDataList();
    useMessage().success('删除成功');
  } catch (err: any) {
    useMessage().error(err.msg);
  }
};
</script>