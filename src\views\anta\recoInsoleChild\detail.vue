<template>
  <el-dialog :title="form.id ? '详情' : '详情'" v-model="visible" :close-on-click-modal="false" draggable :width="1000">
    <el-form ref="dataFormRef" :model="form" formDialogRef label-width="90px" v-loading="loading">
      <el-row :gutter="24">
        <el-col :span="12" class="mb20">
          <el-form-item label="标题" prop="title">
            <el-input v-model="form.title" placeholder="请输入标题" :disabled="true" />
          </el-form-item>
        </el-col>


        <el-col :span="12" class="mb20">
          <el-form-item label="类型" prop="type">
            <el-select v-model="form.type" placeholder="请选择类型" :disabled="true">
              <el-option label="鞋子" value="shoes"></el-option>
              <el-option label="鞋垫" value="insole"></el-option>
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :span="12" class="mb20">
          <el-form-item label="特征" prop="feature">
            <el-select v-model="form.feature" placeholder="请选择特征" :disabled="true">
              <el-option label="大体重" value="大体重"></el-option>
              <el-option label="正常" value="正常"></el-option>
              <el-option label="低足弓" value="低足弓"></el-option>
              <el-option label="高足弓" value="高足弓"></el-option>
            </el-select>
          </el-form-item>
        </el-col>



        <el-col :span="12" class="mb20">
          <el-form-item label="性别" prop="gender">
            <el-select v-model="form.gender" placeholder="请选择性别" :disabled="true">
              <el-option label="男" value="0"></el-option>
              <el-option label="女" value="1"></el-option>
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :span="24" class="mb20">
          <el-form-item label="产品列表" prop="product" :disabled="true">
          </el-form-item>

          <el-row :gutter="20">
            <el-col :span="12" v-for="(item, index) in productParam" :key="index" class="mb20">
              <div class="product-card">
                <el-form-item label='商品编码' :disabled="true">
                  <el-input v-model="item.code" style="width:150px" :disabled="true" />
                </el-form-item>
                <el-form-item label='商品名称' style="margin-top: 10px;" :disabled="true">
                  <el-input v-model="item.name" :disabled="true" />
                </el-form-item>
                <el-form-item label='商品价格' style="margin-top: 10px;">
                  <el-input v-model="item.amount" style="width:150px" :disabled="true" />
                </el-form-item>

                <el-form-item label='商品图片' style="margin-top: 10px;">
                  <el-upload ref="uploadRef" :auto-upload="false" class="avatar-uploader" :show-file-list="false"
                    :on-change="file => onSelectFile(item, file)" :action="item.image" :disabled="true">
                    <img v-if="item.imgFile" :src="item.imgFile" class="product-image" />
                    <img v-else-if="item.image" :src="item.image" class="product-image" />
                    <el-icon v-else class="avatar-uploader-icon">
                      <Plus />
                    </el-icon>
                  </el-upload>
                </el-form-item>
                
                <!-- 添加商品内容展示 -->
                <el-form-item label='商品内容' style="margin-top: 10px;" v-if="item.content && item.content.length">
                  <div class="content-list">
                    <p v-for="(contentItem, contentIndex) in item.content" :key="contentIndex" class="content-item">
                      {{contentItem.content}}
                    </p>
                  </div>
                </el-form-item>
              </div>
            </el-col>
          </el-row>
        </el-col>
      </el-row>
    </el-form>
    <!-- <template #footer>
      <span class="dialog-footer">
        <el-button @click="cancel">取消</el-button>
        <el-button type="primary" @click="onSubmit" :disabled="loading">确认</el-button>
      </span>
    </template> -->
  </el-dialog>
</template>

<script setup lang="ts" name="RecommendDialog">
import { useDict } from '/@/hooks/dict';
import { useMessage } from "/@/hooks/message";
import { getObj1, addObj, putObj, uploadObj } from '/@/api/anta/recommend'
import { rule } from '/@/utils/validate';
const emit = defineEmits(['refresh']);

// 定义变量内容
const dataFormRef = ref();
const visible = ref(false)
const loading = ref(false)
// 定义字典
const { gender } = useDict('gender')

// 提交表单数据
const form = reactive({
  id: '',
  title: '',
  subTitle: '',
  gender: '',
  product: '',
  introduce: '',
  type: '',
  feature: '',
});

const productParam = reactive([]);
const introduceParam = reactive([]);

const uploadRef = ref()

const onSelectFile = (item, file) => {
  // 基于 FileReader 读取图片做预览
  const reader = new FileReader();
  reader.onload = (e) => {
    item.image = e?.target?.result;

    const formData = new FormData();
    formData.append('file', file.raw);
    uploadObj(formData).then(response => {
      item.image = response.data.url;
    })
  };
  reader.readAsDataURL(file.raw);
}



// 打开弹窗
const openDialog = (id: string) => {
  visible.value = true
  form.id = ''

  // 重置表单数据
  nextTick(() => {
    dataFormRef.value?.resetFields();
  });

  // 获取recommend信息
  if (id) {
    form.id = id
    getrecommendData(id);
    productParam.length = 0;
    introduceParam.length = 0;
  }
};



const getrecommendData = async (id: string) => {
  loading.value = true;
  try {
    const res = await getObj1(id);
    Object.assign(form, res.data);
    const parsedProductParam = JSON.parse(form.product);
    productParam.push(...parsedProductParam);

    const parsedIntroduceParam = JSON.parse(form.introduce);
    introduceParam.push(...parsedIntroduceParam);

  } catch (error) {
    console.log('Error occurred while getting data:', error);
  } finally {
    loading.value = false;
  }
};

// 暴露变量
defineExpose({
  openDialog
});
</script>

<style lang="scss" scoped>
.preview-image {
  height: 100px;
  width: 100px;
  margin-top: -10px;
}

.upload-icon {
  font-size: 50px;
}

.upload-image-container {
  position: relative;
  width: 2%;
  height: 200px;
}

.upload-image-container .el-icon-plus {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.avatar-uploader {
  :deep() {
    .avatar {
      width: 180px;
      height: 125px;
      display: block;
    }

    .el-upload {
      border: 1px dashed var(--el-border-color);
      border-radius: 6px;
      cursor: pointer;
      position: relative;
      overflow: hidden;
      transition: var(--el-transition-duration-fast);
    }

    .el-upload:hover {
      border-color: var(--el-color-primary);
    }

    .el-icon.avatar-uploader-icon {
      font-size: 28px;
      color: #8c939d;
      width: 120px;
      height: 120px;
      text-align: center;
    }
  }
}

.device-id-item {
  margin-top: 10px;
  margin-bottom: 10px;

  :deep(.el-select) {
    width: 150px !important;

    .el-select__tags {
      max-height: 36px;
      overflow-x: hidden;
      flex-wrap: nowrap;
      white-space: nowrap;
      padding-left: 8px;
      margin-left: 4px;

      .el-tag {
        display: inline-flex;
        max-width: 80px;
        margin-right: 4px;
        margin-left: 2px;
      }

      .el-select__tags-text {
        display: inline-block;
        max-width: 60px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }

    .el-input__wrapper {
      height: 36px;
      padding: 0 8px;
    }

    .el-select__tags-collapse-tags {
      display: inline-flex;
      align-items: center;
      max-width: 60px;
      margin-left: 4px;

      .el-tag {
        display: inline-flex;
        max-width: 60px;

        .el-tag__content {
          overflow: hidden;
          text-overflow: ellipsis;
        }
      }
    }
  }
}

.product-card {
  border: 1px solid #ebeef5;
  border-radius: 4px;
  padding: 15px;
  background-color: #fff;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  height: 100%;
}

.product-image {
  width: 120px;
  height: 120px;
  display: block;
  object-fit: contain;
}

.content-list {
  width: 100%;
  padding: 8px;
  background-color: #f8f8f8;
  border-radius: 4px;
}

.content-item {
  margin: 0;
  padding: 8px 0;
  border-bottom: 1px dashed #eeeeee;
  white-space: normal;
  word-break: break-all;
  line-height: 1.8;
  color: #606266;
}

.content-item:last-child {
  border-bottom: none;
}
</style>